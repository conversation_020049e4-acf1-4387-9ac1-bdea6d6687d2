<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login
requireLogin(true);

require_once '../../config/db.php';

// Get data siswa untuk dropdown
$siswa_result = $conn->query("SELECT s.nisn, s.nama_lengkap, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.status = 'Aktif' ORDER BY s.nama_lengkap");

// Pre-select siswa jika ada parameter
$selected_siswa = $_GET['siswa_nisn'] ?? '';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah Absensi - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0 8px 8px 0;
        }
        .required {
            color: #dc3545;
        }
        .kehadiran-option {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        .kehadiran-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .kehadiran-option.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .kehadiran-option input[type="radio"] {
            display: none;
        }
        .kehadiran-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .kehadiran-hadir .kehadiran-icon { color: #28a745; }
        .kehadiran-sakit .kehadiran-icon { color: #dc3545; }
        .kehadiran-izin .kehadiran-icon { color: #ffc107; }
        .kehadiran-alpa .kehadiran-icon { color: #6c757d; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link active" href="index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-calendar-plus text-primary me-2"></i>Tambah Absensi</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/index.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Absensi</a></li>
                        <li class="breadcrumb-item active">Tambah Absensi</li>
                    </ol>
                </nav>
            </div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Kembali
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>Terjadi kesalahan saat menyimpan absensi!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Form Tambah Absensi -->
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Form Tambah Absensi</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="../../controllers/AbsensiController.php" id="formTambahAbsensi">
                    <!-- Informasi Dasar -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-info-circle me-2"></i>Informasi Dasar</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Tanggal <span class="required">*</span></label>
                                <input type="date" class="form-control" name="tanggal" value="<?= date('Y-m-d') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Jam Kehadiran</label>
                                <div class="row">
                                    <div class="col-6">
                                        <label class="form-label small">Jam Masuk (Opsional)</label>
                                        <input type="time" class="form-control" name="jam_masuk">
                                    </div>
                                    <div class="col-6">
                                        <label class="form-label small">Jam Keluar (Opsional)</label>
                                        <input type="time" class="form-control" name="jam_keluar">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pilih Siswa -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-user me-2"></i>Pilih Siswa</h6>
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Nama Siswa <span class="required">*</span></label>
                                <select class="form-select" name="siswa_nisn" required id="selectSiswa">
                                    <option value="">-- Pilih Siswa --</option>
                                    <?php while ($siswa = $siswa_result->fetch_assoc()): ?>
                                        <option value="<?= $siswa['nisn'] ?>" <?= $selected_siswa == $siswa['nisn'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($siswa['nama_lengkap']) ?> 
                                            <?= $siswa['nama_kelas'] ? '(' . $siswa['nama_kelas'] . ')' : '' ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Jenis Kehadiran <span class="required">*</span></label>
                                <select class="form-select" name="jenis_kehadiran" id="jenisKehadiran">
                                    <option value="">-- Pilih Jenis --</option>
                                    <option value="Hadir">Hadir</option>
                                    <option value="Sakit">Sakit</option>
                                    <option value="Izin">Izin</option>
                                    <option value="Alpa">Alpha</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Jenis Kehadiran (Visual) -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-check-circle me-2"></i>Jenis Kehadiran</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="kehadiran-option kehadiran-hadir text-center" data-value="Hadir">
                                    <input type="radio" name="keterangan" value="Hadir" required>
                                    <div class="kehadiran-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <h6>Hadir</h6>
                                    <small class="text-muted">Siswa hadir tepat waktu</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="kehadiran-option kehadiran-sakit text-center" data-value="Sakit">
                                    <input type="radio" name="keterangan" value="Sakit" required>
                                    <div class="kehadiran-icon">
                                        <i class="fas fa-thermometer-half"></i>
                                    </div>
                                    <h6>Sakit</h6>
                                    <small class="text-muted">Tidak hadir karena sakit</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="kehadiran-option kehadiran-izin text-center" data-value="Izin">
                                    <input type="radio" name="keterangan" value="Izin" required>
                                    <div class="kehadiran-icon">
                                        <i class="fas fa-hand-paper"></i>
                                    </div>
                                    <h6>Izin</h6>
                                    <small class="text-muted">Tidak hadir dengan izin</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="kehadiran-option kehadiran-alpa text-center" data-value="Alpa">
                                    <input type="radio" name="keterangan" value="Alpa" required>
                                    <div class="kehadiran-icon">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <h6>Alpha</h6>
                                    <small class="text-muted">Tidak hadir tanpa keterangan</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Keterangan -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-comment me-2"></i>Keterangan (Opsional)</h6>
                        <div class="row">
                            <div class="col-12">
                                <label class="form-label">Catatan Tambahan</label>
                                <textarea class="form-control" name="catatan" rows="3" 
                                          placeholder="Berikan keterangan tambahan jika diperlukan..."></textarea>
                                <div class="form-text">
                                    Contoh: "Sakit demam dengan surat dokter", "Izin keperluan keluarga", dll.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informasi Tambahan -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Informasi Kehadiran</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Hadir:</strong> Siswa hadir tepat waktu</li>
                                    <li><strong>Sakit:</strong> Tidak hadir karena sakit dengan surat keterangan</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Izin:</strong> Tidak hadir dengan izin yang sah</li>
                                    <li><strong>Alpha:</strong> Tidak hadir tanpa keterangan</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Tombol Submit -->
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="fas fa-times me-1"></i>Batal
                        </button>
                        <button type="submit" name="tambah_absensi" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Simpan Absensi
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle kehadiran option selection
        document.querySelectorAll('.kehadiran-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                document.querySelectorAll('.kehadiran-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Update dropdown
                const jenisKehadiran = document.getElementById('jenisKehadiran');
                jenisKehadiran.value = radio.value;
            });
        });

        // Sync dropdown with radio buttons
        document.getElementById('jenisKehadiran').addEventListener('change', function() {
            const value = this.value;
            const option = document.querySelector(`.kehadiran-option[data-value="${value}"]`);
            
            if (option) {
                // Remove selected class from all options
                document.querySelectorAll('.kehadiran-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add selected class and check radio
                option.classList.add('selected');
                option.querySelector('input[type="radio"]').checked = true;
            }
        });

        // Form validation
        document.getElementById('formTambahAbsensi').addEventListener('submit', function(e) {
            const siswa = document.querySelector('[name="siswa_nisn"]').value;
            const keterangan = document.querySelector('[name="keterangan"]:checked');

            if (!siswa) {
                e.preventDefault();
                alert('Mohon pilih siswa!');
                return false;
            }

            if (!keterangan) {
                e.preventDefault();
                alert('Mohon pilih jenis kehadiran!');
                return false;
            }
        });
    </script>
</body>
</html>
