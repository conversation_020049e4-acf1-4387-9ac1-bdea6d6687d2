<?php
session_start();
require_once '../config/db.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create':
            createKelas();
            break;
        case 'update':
            updateKelas();
            break;
        default:
            header('Location: ../views/kelas/index.php');
            break;
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'GET') {
    if (isset($_GET['hapus'])) {
        deleteKelas($_GET['hapus']);
    }
}

function createKelas() {
    global $conn;

    $nama_kelas = $_POST['nama_kelas'] ?? '';
    $tingkat = $_POST['tingkat'] ?? '';
    $kurikulum = $_POST['kurikulum'] ?? '';
    $tahun_pelajaran = $_POST['tahun_pelajaran'] ?? '';
    $kapasitas = $_POST['kapasitas'] ?? 30;
    $wali_kelas = $_POST['wali_kelas'] ?? '';

    $query = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, kapasitas, wali_kelas)
              VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($query);

    if (!$stmt) {
        header('Location: ../views/kelas/tambah.php?error=Error preparing statement: ' . $conn->error);
        return;
    }

    $stmt->bind_param("ssssss", $nama_kelas, $tingkat, $kurikulum, $tahun_pelajaran, $kapasitas, $wali_kelas);

    if ($stmt->execute()) {
        header('Location: ../views/kelas/index.php?success=Kelas berhasil ditambahkan');
    } else {
        header('Location: ../views/kelas/tambah.php?error=Gagal menambahkan kelas: ' . $stmt->error);
    }

    $stmt->close();
}

function updateKelas() {
    global $conn;

    $id = $_POST['id'] ?? '';
    $nama_kelas = $_POST['nama_kelas'] ?? '';
    $tingkat = $_POST['tingkat'] ?? '';
    $kurikulum = $_POST['kurikulum'] ?? '';
    $tahun_pelajaran = $_POST['tahun_pelajaran'] ?? '';
    $kapasitas = $_POST['kapasitas'] ?? 30;
    $wali_kelas = $_POST['wali_kelas'] ?? '';

    $query = "UPDATE kelas SET nama_kelas = ?, tingkat = ?, kurikulum = ?, tahun_pelajaran = ?, kapasitas = ?, wali_kelas = ? WHERE id = ?";

    $stmt = $conn->prepare($query);

    if (!$stmt) {
        header('Location: ../views/kelas/edit.php?id=' . $id . '&error=Error preparing statement: ' . $conn->error);
        return;
    }

    $stmt->bind_param("ssssssi", $nama_kelas, $tingkat, $kurikulum, $tahun_pelajaran, $kapasitas, $wali_kelas, $id);

    if ($stmt->execute()) {
        header('Location: ../views/kelas/index.php?success=Kelas berhasil diperbarui');
    } else {
        header('Location: ../views/kelas/edit.php?id=' . $id . '&error=Gagal mengupdate kelas: ' . $stmt->error);
    }

    $stmt->close();
}

function deleteKelas($id) {
    global $conn;

    // Check if there are students in this class
    $check_query = "SELECT COUNT(*) as count FROM siswa WHERE kelas_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $id);
    $check_stmt->execute();
    $result = $check_stmt->get_result()->fetch_assoc();

    if ($result['count'] > 0) {
        header('Location: ../views/kelas/index.php?error=Tidak dapat menghapus kelas yang masih memiliki siswa');
        return;
    }

    $query = "DELETE FROM kelas WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        header('Location: ../views/kelas/index.php?success=Kelas berhasil dihapus');
    } else {
        header('Location: ../views/kelas/index.php?error=Gagal menghapus kelas');
    }
}
?>