<?php
echo "<h1>🔧 Aplikasi Siswa - Diagnostic Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    require_once 'config/db.php';
    echo "✅ Database connection: <strong>SUCCESS</strong><br>";
    
    // Test tables
    $tables = ['users', 'siswa', 'kelas', 'absensi', 'catatan_siswa', 'berkas_siswa'];
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "✅ Table '$table': <strong>$count records</strong><br>";
        } else {
            echo "❌ Table '$table': <strong>ERROR</strong><br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database connection: <strong>FAILED</strong> - " . $e->getMessage() . "<br>";
}

// Test 2: File Structure
echo "<h2>2. File Structure Test</h2>";
$required_files = [
    'index.php',
    'config/db.php',
    'config/functions.php',
    'controllers/AuthController.php',
    'controllers/SiswaController.php',
    'controllers/AbsensiController.php',
    'controllers/CatatanController.php',
    'controllers/BerkasController.php',
    'views/auth/login.php',
    'views/dashboard/index.php',
    'views/siswa/index.php',
    'views/siswa/detail.php',
    'views/absensi/index.php',
    'views/absensi/tambah.php',
    'views/catatan/index.php',
    'views/catatan/tambah.php',
    'views/berkas/index.php',
    'views/berkas/upload.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ File '$file': <strong>EXISTS</strong><br>";
    } else {
        echo "❌ File '$file': <strong>MISSING</strong><br>";
    }
}

// Test 3: Upload Directories
echo "<h2>3. Upload Directories Test</h2>";
$upload_dirs = [
    'uploads',
    'uploads/foto',
    'uploads/identitas',
    'uploads/rapor',
    'uploads/ijazah',
    'uploads/lainnya'
];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'WRITABLE' : 'NOT WRITABLE';
        echo "✅ Directory '$dir': <strong>EXISTS ($writable)</strong><br>";
    } else {
        echo "❌ Directory '$dir': <strong>MISSING</strong><br>";
    }
}

// Test 4: Sample Data
echo "<h2>4. Sample Data Test</h2>";
try {
    $user_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $siswa_count = $conn->query("SELECT COUNT(*) as count FROM siswa")->fetch_assoc()['count'];
    $kelas_count = $conn->query("SELECT COUNT(*) as count FROM kelas")->fetch_assoc()['count'];
    
    echo "✅ Users: <strong>$user_count</strong><br>";
    echo "✅ Siswa: <strong>$siswa_count</strong><br>";
    echo "✅ Kelas: <strong>$kelas_count</strong><br>";
    
    if ($user_count > 0 && $siswa_count > 0 && $kelas_count > 0) {
        echo "<br>🎉 <strong>Sample data is available!</strong><br>";
        echo "Login credentials: <strong>admin / admin123</strong><br>";
    } else {
        echo "<br>⚠️ <strong>Sample data missing. Run create_sample_data.php</strong><br>";
    }
} catch (Exception $e) {
    echo "❌ Sample data test: <strong>FAILED</strong> - " . $e->getMessage() . "<br>";
}

// Test 5: Navigation Links
echo "<h2>5. Application Links</h2>";
echo "🔗 <a href='index.php'>Home Page</a><br>";
echo "🔗 <a href='views/auth/login.php'>Login Page</a><br>";
echo "🔗 <a href='views/dashboard/index.php'>Dashboard</a><br>";
echo "🔗 <a href='views/siswa/index.php'>Siswa Management</a><br>";
echo "🔗 <a href='views/absensi/index.php'>Absensi Management</a><br>";
echo "🔗 <a href='views/catatan/index.php'>Catatan Management</a><br>";
echo "🔗 <a href='views/berkas/index.php'>Berkas Management</a><br>";

echo "<h2>6. Server Information</h2>";
echo "PHP Version: <strong>" . phpversion() . "</strong><br>";
echo "Server: <strong>" . $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' . "</strong><br>";
echo "Document Root: <strong>" . $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' . "</strong><br>";

echo "<hr>";
echo "<p><strong>🚀 If all tests show ✅, your application is ready to use!</strong></p>";
echo "<p>Access the application at: <a href='http://localhost:8000'>http://localhost:8000</a></p>";
?>
