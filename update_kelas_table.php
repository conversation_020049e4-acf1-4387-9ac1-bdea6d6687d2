<?php
require_once 'config/db.php';

echo "=== UPDATE STRUKTUR TABEL KELAS ===\n";

// Cek kolom yang ada
$columns_result = $conn->query("DESCRIBE kelas");
$existing_columns = [];
while ($row = $columns_result->fetch_assoc()) {
    $existing_columns[] = $row['Field'];
}

echo "Kolom yang ada: " . implode(', ', $existing_columns) . "\n\n";

// Tambahkan kolom yang diperlukan
$columns_to_add = [
    'tingkat' => "VARCHAR(20) DEFAULT NULL AFTER nama_kelas",
    'kurikulum' => "VARCHAR(100) DEFAULT NULL AFTER tingkat", 
    'tahun_pelajaran' => "VARCHAR(20) DEFAULT '2024/2025' AFTER kurikulum",
    'kapasitas' => "INT DEFAULT 30 AFTER tahun_pelajaran"
];

foreach ($columns_to_add as $column => $definition) {
    if (!in_array($column, $existing_columns)) {
        $sql = "ALTER TABLE kelas ADD COLUMN $column $definition";
        echo "Menambahkan kolom $column...\n";
        
        if ($conn->query($sql)) {
            echo "✓ Kolom $column berhasil ditambahkan\n";
        } else {
            echo "✗ Error menambahkan kolom $column: " . $conn->error . "\n";
        }
    } else {
        echo "Kolom $column sudah ada\n";
    }
}

// Update data kelas yang sudah ada dengan nilai default
echo "\nUpdate data kelas yang sudah ada...\n";

$update_queries = [
    "UPDATE kelas SET tingkat = 'X-1' WHERE nama_kelas = 'Kelas X-1'",
    "UPDATE kelas SET tingkat = 'X-2' WHERE nama_kelas = 'Kelas X-2'", 
    "UPDATE kelas SET tingkat = 'XI-1' WHERE nama_kelas = 'Kelas XI-1'",
    "UPDATE kelas SET tingkat = 'XI-2' WHERE nama_kelas = 'Kelas XI-2'",
    "UPDATE kelas SET tingkat = 'XII-1' WHERE nama_kelas = 'Kelas XII-1'",
    "UPDATE kelas SET tingkat = 'XII-2' WHERE nama_kelas = 'Kelas XII-2'",
    "UPDATE kelas SET kurikulum = 'Kurikulum Merdeka' WHERE kurikulum IS NULL",
    "UPDATE kelas SET tahun_pelajaran = '2024/2025' WHERE tahun_pelajaran IS NULL",
    "UPDATE kelas SET kapasitas = 30 WHERE kapasitas IS NULL"
];

foreach ($update_queries as $query) {
    if ($conn->query($query)) {
        echo "✓ Update berhasil: " . substr($query, 0, 50) . "...\n";
    } else {
        echo "✗ Error update: " . $conn->error . "\n";
    }
}

// Tampilkan struktur tabel yang baru
echo "\n=== STRUKTUR TABEL KELAS SETELAH UPDATE ===\n";
$result = $conn->query("DESCRIBE kelas");
while ($row = $result->fetch_assoc()) {
    echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
}

// Tampilkan data kelas
echo "\n=== DATA KELAS ===\n";
$result = $conn->query("SELECT * FROM kelas");
while ($row = $result->fetch_assoc()) {
    echo "ID: " . $row['id'] . " | Nama: " . $row['nama_kelas'] . " | Tingkat: " . ($row['tingkat'] ?? 'NULL') . " | Kurikulum: " . ($row['kurikulum'] ?? 'NULL') . "\n";
}

echo "\n🎉 Update tabel kelas selesai!\n";
$conn->close();
?>
