<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Data Siswa - <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        
        .profile-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .profile-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .profile-id {
            opacity: 0.9;
            margin-bottom: 0.25rem;
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
            border-radius: 10px 10px 0 0;
            background: #f8f9fa;
            transition: all 0.3s;
        }
        
        .nav-tabs .nav-link.active {
            background: white;
            color: #007bff;
            border-bottom: 3px solid #007bff;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 15px 15px 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .data-row {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }
        
        .bg-blue { background: linear-gradient(135deg, #007bff, #0056b3); }
        .bg-green { background: linear-gradient(135deg, #28a745, #1e7e34); }
        .bg-orange { background: linear-gradient(135deg, #fd7e14, #e55a00); }
        .bg-purple { background: linear-gradient(135deg, #6f42c1, #5a2d91); }
        
        .data-content {
            flex: 1;
        }
        
        .data-label {
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        
        .data-value {
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Data Siswa</a>
                <a class="nav-link" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Data Absensi</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h4 class="mb-4">Preview Data Siswa</h4>

        <div class="row">
            <!-- Profile Card -->
            <div class="col-md-3">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <div class="profile-name">Ahmad Rizki Pratama</div>
                    <div class="profile-id">NIS: A25.285</div>
                    <div class="profile-id">Kelas: X-1</div>

                    <hr style="border-color: rgba(255,255,255,0.3);">

                    <div class="d-grid gap-2">
                        <button class="btn btn-light btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit Data Siswa
                        </button>
                        <button class="btn btn-outline-light btn-sm">
                            <i class="fas fa-upload me-2"></i>Upload Berkas
                        </button>
                        <button class="btn btn-outline-light btn-sm">
                            <i class="fas fa-calendar-check me-2"></i>Tambah Catatan
                        </button>
                        <button class="btn btn-outline-light btn-sm">
                            <i class="fas fa-download me-2"></i>Kompilasi Daftar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="siswaTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab">
                            <i class="fas fa-user me-2"></i>Informasi Personal
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" type="button" role="tab">
                            <i class="fas fa-calendar-check me-2"></i>Data Absensi
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" type="button" role="tab">
                            <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" type="button" role="tab">
                            <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" type="button" role="tab">
                            <i class="fas fa-folder me-2"></i>Berkas Siswa
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="siswaTabContent">
                    <!-- Informasi Personal -->
                    <div class="tab-pane fade" id="personal" role="tabpanel">
                        <h6><i class="fas fa-id-card me-2"></i>Data Identitas</h6>
                        <p class="text-muted">Informasi personal siswa akan ditampilkan di sini.</p>
                    </div>

                    <!-- Data Absensi -->
                    <div class="tab-pane fade show active" id="absensi" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6><i class="fas fa-calendar-check me-2"></i>Data Absensi</h6>
                            <button class="btn btn-primary btn-sm" onclick="tambahAbsensi()">
                                <i class="fas fa-plus me-1"></i>Tambah Absensi
                            </button>
                        </div>

                        <!-- Statistik Absensi -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center" style="border-left: 4px solid #28a745;">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-center mb-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, #4CAF50, #45a049);">
                                                <i class="fas fa-check-circle text-white"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-success">100.0%</h4>
                                        <small class="text-muted">Kehadiran</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center" style="border-left: 4px solid #dc3545;">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-center mb-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, #f44336, #d32f2f);">
                                                <i class="fas fa-thermometer-half text-white"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-danger">0</h4>
                                        <small class="text-muted">Sakit</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center" style="border-left: 4px solid #ffc107;">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-center mb-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, #ff9800, #f57c00);">
                                                <i class="fas fa-hand-paper text-white"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-warning">0</h4>
                                        <small class="text-muted">Ijin</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center" style="border-left: 4px solid #6c757d;">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-center mb-2">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; background: linear-gradient(135deg, #9e9e9e, #757575);">
                                                <i class="fas fa-times-circle text-white"></i>
                                            </div>
                                        </div>
                                        <h4 class="text-secondary">0</h4>
                                        <small class="text-muted">Alpha</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Riwayat Absensi -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Absensi</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h6>Belum ada catatan absensi</h6>
                                    <p class="text-muted">Siswa ini belum memiliki catatan ketidakhadiran</p>
                                    <button class="btn btn-primary" onclick="tambahAbsensi()">
                                        <i class="fas fa-plus me-1"></i>Tambah Absensi Pertama
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab lainnya -->
                    <div class="tab-pane fade" id="akademik" role="tabpanel">
                        <h6><i class="fas fa-graduation-cap me-2"></i>Informasi Akademik</h6>
                        <p class="text-muted">Fitur akan segera tersedia.</p>
                    </div>

                    <div class="tab-pane fade" id="catatan" role="tabpanel">
                        <h6><i class="fas fa-sticky-note me-2"></i>Catatan Siswa</h6>
                        <p class="text-muted">Fitur akan segera tersedia.</p>
                    </div>

                    <div class="tab-pane fade" id="berkas" role="tabpanel">
                        <h6><i class="fas fa-folder me-2"></i>Berkas Siswa</h6>
                        <p class="text-muted">Fitur akan segera tersedia.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function tambahAbsensi() {
            Swal.fire({
                title: 'Tambah Absensi',
                text: 'Fitur tambah absensi akan mengarahkan ke form absensi.',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        }
    </script>
</body>
</html>
