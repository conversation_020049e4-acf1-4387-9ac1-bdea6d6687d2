<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Berkas Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            border-radius: 15px;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .file-drop-area {
            cursor: pointer;
            transition: all 0.3s;
        }
        .file-drop-area:hover {
            background-color: #f8f9fa;
        }
        .nav-tabs .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
            border-color: #007bff;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Siswa</a>
                <a class="nav-link" href="#">Catatan</a>
                <a class="nav-link" href="#">Absensi</a>
                <a class="nav-link active" href="#">Berkas</a>
                <a class="nav-link" href="#">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Tab Navigation -->
        <div class="mb-4">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-user me-2"></i>Informasi Personal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-calendar-check me-2"></i>Data Absensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="#" style="background-color: #007bff; color: white;">
                        <i class="fas fa-folder me-2"></i>Berkas Siswa
                    </a>
                </li>
            </ul>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-file-alt" style="color: #667eea; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">1</h3>
                                <small>Total Berkas</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-layer-group" style="color: #f093fb; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">1</h3>
                                <small>Kategori</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-clock" style="color: #4facfe; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">05/07/2025</h3>
                                <small>Terakhir Upload</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-folder text-primary me-2"></i>Daftar Berkas</h4>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                <i class="fas fa-upload me-1"></i>Upload Berkas
            </button>
        </div>

        <!-- Berkas List -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Berkas</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Dokumen Identitas</th>
                                        <th>Kartu Keluarga</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                <span>1 berkas</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-image text-warning me-2"></i>
                                                <span>-</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" title="Lihat">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" title="Download">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" title="Hapus">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Format yang didukung:</small>
                            <div class="mt-1">
                                <span class="badge bg-light text-dark me-1">PDF</span>
                                <span class="badge bg-light text-dark me-1">JPG</span>
                                <span class="badge bg-light text-dark me-1">PNG</span>
                                <span class="badge bg-light text-dark me-1">DOC</span>
                                <span class="badge bg-light text-dark">DOCX</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Ukuran maksimal: <strong>5MB</strong></small>
                        </div>
                        <hr>
                        <div class="text-center">
                            <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                            <p class="small text-muted mb-0">File Anda aman dan terenkripsi</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div class="modal fade" id="uploadModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Upload Berkas Siswa</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" enctype="multipart/form-data" id="formUploadBerkas">
                        <div class="modal-body">
                            <!-- Jenis Berkas -->
                            <div class="mb-3">
                                <label class="form-label">Jenis Berkas <span class="text-danger">*</span></label>
                                <select class="form-select" name="jenis_berkas" required>
                                    <option value="">Pilih Jenis Berkas</option>
                                    <option value="Dokumen Identitas">Dokumen Identitas</option>
                                    <option value="Kartu Keluarga" selected>Kartu Keluarga</option>
                                    <option value="Akta Kelahiran">Akta Kelahiran</option>
                                    <option value="Rapor Kelas X">Rapor Kelas X</option>
                                    <option value="Rapor Kelas XI">Rapor Kelas XI</option>
                                    <option value="Rapor Kelas XII">Rapor Kelas XII</option>
                                    <option value="Rapor KPP">Rapor KPP</option>
                                    <option value="Rapor KPA">Rapor KPA</option>
                                    <option value="Ijazah SD/MI">Ijazah SD/MI</option>
                                    <option value="Ijazah SMP/MTs">Ijazah SMP/MTs</option>
                                    <option value="Ijazah SMA/SMK/MA">Ijazah SMA/SMK/MA</option>
                                    <option value="Foto Siswa">Foto Siswa</option>
                                    <option value="Surat Keterangan Sehat">Surat Keterangan Sehat</option>
                                    <option value="Surat Kelakuan Baik">Surat Kelakuan Baik</option>
                                    <option value="Surat Peringatan 1">Surat Peringatan 1</option>
                                    <option value="Surat Peringatan 2">Surat Peringatan 2</option>
                                    <option value="Surat Peringatan 3">Surat Peringatan 3</option>
                                    <option value="Surat Panggilan Orang Tua">Surat Panggilan Orang Tua</option>
                                    <option value="Piagam Prestasi">Piagam Prestasi</option>
                                    <option value="Sertifikat Lomba">Sertifikat Lomba</option>
                                    <option value="Penghargaan Akademik">Penghargaan Akademik</option>
                                    <option value="Dokumen Lainnya">Dokumen Lainnya</option>
                                </select>
                            </div>

                            <!-- File Berkas -->
                            <div class="mb-3">
                                <label class="form-label">File Berkas <span class="text-danger">*</span></label>
                                <div class="file-drop-area border-2 border-dashed border-success rounded p-4 text-center" id="fileDropArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                    <h6>Drag & Drop file di sini</h6>
                                    <p class="text-muted mb-3">atau klik untuk memilih file</p>
                                    <input type="file" name="file_berkas" id="fileInput" class="d-none" required
                                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <button type="button" class="btn btn-outline-success" onclick="document.getElementById('fileInput').click()">
                                        <i class="fas fa-folder-open me-1"></i>Choose File
                                    </button>
                                    <div class="form-text mt-2">
                                        Format: PDF, JPG, PNG, DOC, DOCX. Maksimal 5MB
                                    </div>
                                    <div class="form-text">
                                        No file chosen
                                    </div>
                                </div>
                            </div>

                            <!-- Keterangan -->
                            <div class="mb-3">
                                <label class="form-label">Keterangan (Optional)</label>
                                <textarea class="form-control" name="keterangan" rows="3"
                                          placeholder="Tambahkan keterangan untuk berkas ini..."></textarea>
                            </div>

                            <!-- Preview File -->
                            <div class="mb-3">
                                <label class="form-label">Preview File</label>
                                <div class="border rounded p-4 text-center bg-light">
                                    <i class="fas fa-file fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">Preview file akan muncul di sini</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" name="upload_berkas" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>Upload Berkas
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-open modal for demo
        document.addEventListener('DOMContentLoaded', function() {
            var uploadModal = new bootstrap.Modal(document.getElementById('uploadModal'));
            uploadModal.show();
        });

        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const fileDropArea = document.getElementById('fileDropArea');

        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const fileName = this.files[0].name;
                fileDropArea.querySelector('.form-text:last-child').textContent = fileName;
            }
        });
    </script>
</body>
</html>
