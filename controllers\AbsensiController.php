<?php
require_once '../config/db.php';
require_once '../config/functions.php';
session_start();

// Tambah Absensi
if (isset($_POST['tambah_absensi'])) {
    $siswa_nisn = $_POST['siswa_nisn'];
    $tanggal = $_POST['tanggal'];
    $keterangan = $_POST['keterangan'];
    $catatan = $_POST['catatan'] ?? null;
    $jam_masuk = !empty($_POST['jam_masuk']) ? $_POST['jam_masuk'] : null;
    $jam_keluar = !empty($_POST['jam_keluar']) ? $_POST['jam_keluar'] : null;

    // Cek apakah sudah ada absensi untuk siswa di tanggal tersebut
    $check_stmt = $conn->prepare("SELECT id FROM absensi WHERE siswa_nisn = ? AND tanggal = ?");
    $check_stmt->bind_param("ss", $siswa_nisn, $tanggal);
    $check_stmt->execute();
    $existing = $check_stmt->get_result()->fetch_assoc();

    if ($existing) {
        header("Location: ../views/absensi/tambah.php?error=duplicate");
        exit;
    }

    $stmt = $conn->prepare("INSERT INTO absensi (siswa_nisn, tanggal, keterangan, catatan, jam_masuk, jam_keluar) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssss", $siswa_nisn, $tanggal, $keterangan, $catatan, $jam_masuk, $jam_keluar);

    if ($stmt->execute()) {
        // Update statistik absensi
        updateStatistikAbsensi($conn, $siswa_nisn, $tanggal);
        header("Location: ../views/absensi/index.php?success=1");
    } else {
        header("Location: ../views/absensi/tambah.php?error=1");
    }
    exit;
}

// Edit Absensi
if (isset($_POST['edit_absensi'])) {
    $id = $_POST['id'];
    $keterangan = $_POST['keterangan'];
    $catatan = $_POST['catatan'] ?? null;
    $jam_masuk = !empty($_POST['jam_masuk']) ? $_POST['jam_masuk'] : null;
    $jam_keluar = !empty($_POST['jam_keluar']) ? $_POST['jam_keluar'] : null;

    $stmt = $conn->prepare("UPDATE absensi SET keterangan=?, catatan=?, jam_masuk=?, jam_keluar=? WHERE id=?");
    $stmt->bind_param("ssssi", $keterangan, $catatan, $jam_masuk, $jam_keluar, $id);

    if ($stmt->execute()) {
        // Get siswa_nisn dan tanggal untuk update statistik
        $get_stmt = $conn->prepare("SELECT siswa_nisn, tanggal FROM absensi WHERE id = ?");
        $get_stmt->bind_param("i", $id);
        $get_stmt->execute();
        $absensi_data = $get_stmt->get_result()->fetch_assoc();

        updateStatistikAbsensi($conn, $absensi_data['siswa_nisn'], $absensi_data['tanggal']);
        header("Location: ../views/absensi/index.php?updated=1");
    } else {
        header("Location: ../views/absensi/edit.php?id=$id&error=1");
    }
    exit;
}

// Hapus Absensi
if (isset($_GET['hapus'])) {
    $id = $_GET['hapus'];

    // Get data absensi sebelum dihapus untuk update statistik
    $get_stmt = $conn->prepare("SELECT siswa_nisn, tanggal FROM absensi WHERE id = ?");
    $get_stmt->bind_param("i", $id);
    $get_stmt->execute();
    $absensi_data = $get_stmt->get_result()->fetch_assoc();

    $stmt = $conn->prepare("DELETE FROM absensi WHERE id = ?");
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        if ($absensi_data) {
            updateStatistikAbsensi($conn, $absensi_data['siswa_nisn'], $absensi_data['tanggal']);
        }
        header("Location: ../views/absensi/index.php?deleted=1");
    } else {
        header("Location: ../views/absensi/index.php?error=1");
    }
    exit;
}

// Get Absensi by Siswa (AJAX)
if (isset($_GET['get_absensi_siswa'])) {
    $siswa_nisn = $_GET['siswa_nisn'];
    $limit = $_GET['limit'] ?? 10;

    $stmt = $conn->prepare("
        SELECT a.*, s.nama_lengkap
        FROM absensi a
        JOIN siswa s ON a.siswa_nisn = s.nisn
        WHERE a.siswa_nisn = ?
        ORDER BY a.tanggal DESC
        LIMIT ?
    ");
    $stmt->bind_param("si", $siswa_nisn, $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $absensi = [];
    while ($row = $result->fetch_assoc()) {
        $row['tanggal'] = date('d/m/Y', strtotime($row['tanggal']));
        $absensi[] = $row;
    }

    header('Content-Type: application/json');
    echo json_encode($absensi);
    exit;
}

// Get Statistik Absensi (AJAX)
if (isset($_GET['get_statistik_absensi'])) {
    $siswa_nisn = $_GET['siswa_nisn'] ?? null;
    $bulan = $_GET['bulan'] ?? date('n');
    $tahun = $_GET['tahun'] ?? date('Y');

    if ($siswa_nisn) {
        $stmt = $conn->prepare("
            SELECT * FROM statistik_absensi
            WHERE siswa_nisn = ? AND bulan = ? AND tahun = ?
        ");
        $stmt->bind_param("sii", $siswa_nisn, $bulan, $tahun);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats = $result->fetch_assoc();

        if (!$stats) {
            // Generate statistik jika belum ada
            updateStatistikAbsensi($conn, $siswa_nisn, date('Y-m-d'));
            $stmt->execute();
            $stats = $stmt->get_result()->fetch_assoc();
        }
    } else {
        // Statistik keseluruhan
        $stmt = $conn->prepare("
            SELECT
                SUM(total_hadir) as total_hadir,
                SUM(total_sakit) as total_sakit,
                SUM(total_izin) as total_izin,
                SUM(total_alpa) as total_alpa,
                AVG(persentase_kehadiran) as avg_kehadiran
            FROM statistik_absensi
            WHERE bulan = ? AND tahun = ?
        ");
        $stmt->bind_param("ii", $bulan, $tahun);
        $stmt->execute();
        $stats = $stmt->get_result()->fetch_assoc();
    }

    header('Content-Type: application/json');
    echo json_encode($stats ?: []);
    exit;
}

// Function untuk update statistik absensi
function updateStatistikAbsensi($conn, $siswa_nisn, $tanggal) {
    $bulan = date('n', strtotime($tanggal));
    $tahun = date('Y', strtotime($tanggal));

    // Hitung ulang statistik untuk bulan tersebut
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_absensi,
            SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
            SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
            SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as total_izin,
            SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as total_alpa
        FROM absensi
        WHERE siswa_nisn = ?
        AND MONTH(tanggal) = ?
        AND YEAR(tanggal) = ?
    ");
    $stmt->bind_param("sii", $siswa_nisn, $bulan, $tahun);
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();

    $total_hari = $stats['total_absensi'] ?: 1;
    $persentase_kehadiran = round(($stats['total_hadir'] / $total_hari) * 100, 2);

    // Insert atau update statistik
    $upsert_stmt = $conn->prepare("
        INSERT INTO statistik_absensi
        (siswa_nisn, bulan, tahun, total_hadir, total_sakit, total_izin, total_alpa, persentase_kehadiran)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        total_hadir = VALUES(total_hadir),
        total_sakit = VALUES(total_sakit),
        total_izin = VALUES(total_izin),
        total_alpa = VALUES(total_alpa),
        persentase_kehadiran = VALUES(persentase_kehadiran),
        updated_at = CURRENT_TIMESTAMP
    ");
    $upsert_stmt->bind_param("siiiiiid",
        $siswa_nisn, $bulan, $tahun,
        $stats['total_hadir'], $stats['total_sakit'],
        $stats['total_izin'], $stats['total_alpa'],
        $persentase_kehadiran
    );
    $upsert_stmt->execute();
}
?>