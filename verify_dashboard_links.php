<?php
/**
 * Script untuk memverifikasi semua link dashboard sudah benar
 */

echo "<h2>🔍 Verifikasi Link Dashboard</h2>";

// Daftar file yang perlu dicek
$files_to_check = [
    'views/siswa/index.php',
    'views/siswa/detail.php', 
    'views/siswa/edit.php',
    'views/siswa/tambah.php',
    'views/kelas/index.php',
    'views/kelas/detail.php',
    'views/kelas/edit.php', 
    'views/kelas/tambah.php',
    'views/absensi/index.php',
    'views/absensi/tambah.php',
    'views/catatan/index.php',
    'views/catatan/tambah.php',
    'views/berkas/index.php',
    'views/berkas/upload.php'
];

echo "<h3>📋 Hasil Pemeriksaan Link Dashboard:</h3>";

$correct_links = 0;
$wrong_links = 0;
$total_files = 0;

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $total_files++;
        $content = file_get_contents($file);
        
        // Cek apakah ada link dashboard yang salah
        $has_wrong_link = false;
        $has_correct_link = false;
        
        // Pattern link yang salah
        if (preg_match('/href=["\']\.\.\/dashboard\.php["\']/', $content) || 
            preg_match('/href=["\']dashboard\.php["\']/', $content)) {
            $has_wrong_link = true;
        }
        
        // Pattern link yang benar
        if (preg_match('/href=["\']\.\.\/dashboard\/index\.php["\']/', $content) ||
            preg_match('/href=["\']dashboard\/index\.php["\']/', $content)) {
            $has_correct_link = true;
        }
        
        if ($has_wrong_link) {
            echo "❌ <strong>$file</strong> - Masih ada link dashboard yang salah<br>";
            $wrong_links++;
        } elseif ($has_correct_link) {
            echo "✅ <strong>$file</strong> - Link dashboard sudah benar<br>";
            $correct_links++;
        } else {
            echo "ℹ️ <strong>$file</strong> - Tidak ada link dashboard<br>";
        }
    } else {
        echo "⚠️ <strong>$file</strong> - File tidak ditemukan<br>";
    }
}

echo "<hr>";
echo "<h3>📊 Ringkasan:</h3>";
echo "<strong>Total files checked:</strong> $total_files<br>";
echo "<strong>✅ Correct links:</strong> $correct_links<br>";
echo "<strong>❌ Wrong links:</strong> $wrong_links<br>";
echo "<strong>📁 Files without dashboard links:</strong> " . ($total_files - $correct_links - $wrong_links) . "<br>";

if ($wrong_links == 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "🎉 <strong>Semua link dashboard sudah benar!</strong>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ <strong>Masih ada $wrong_links file dengan link dashboard yang salah</strong>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 Test Links:</h3>";
echo "<p><a href='views/dashboard/index.php' target='_blank'>🎯 Dashboard Utama (views/dashboard/index.php)</a></p>";
echo "<p><a href='views/dashboard.php' target='_blank'>🔄 Dashboard Redirect (views/dashboard.php)</a></p>";
echo "<p><a href='views/siswa/index.php' target='_blank'>👥 Data Siswa (test navbar)</a></p>";
echo "<p><a href='views/kelas/index.php' target='_blank'>🏫 Data Kelas (test navbar)</a></p>";

echo "<hr>";
echo "<h3>🛠️ Jika Masih Ada Masalah:</h3>";
echo "<ol>";
echo "<li>Jalankan <code>php fix_dashboard_links.php</code> lagi</li>";
echo "<li>Cek manual file yang masih error</li>";
echo "<li>Pastikan semua link menggunakan <code>../dashboard/index.php</code></li>";
echo "</ol>";

// Cek apakah file redirect sudah ada
echo "<h3>📄 File Redirect:</h3>";
if (file_exists('views/dashboard.php')) {
    echo "✅ File redirect <strong>views/dashboard.php</strong> sudah ada<br>";
    $redirect_content = file_get_contents('views/dashboard.php');
    if (strpos($redirect_content, 'dashboard/index.php') !== false) {
        echo "✅ Redirect content sudah benar<br>";
    } else {
        echo "❌ Redirect content perlu diperbaiki<br>";
    }
} else {
    echo "❌ File redirect <strong>views/dashboard.php</strong> belum ada<br>";
}

echo "<hr>";
echo "<p><em>Last checked: " . date('Y-m-d H:i:s') . "</em></p>";
?>
