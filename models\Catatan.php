<?php
class Catatan {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    // Get all catatan with pagination
    public function getAllCatatan($page = 1, $limit = 10, $search = '', $filter_jenis = '', $filter_status = '') {
        $offset = ($page - 1) * $limit;
        
        $where_conditions = [];
        $params = [];
        $types = '';
        
        if (!empty($search)) {
            $where_conditions[] = "(c.judul_catatan LIKE ? OR c.isi_catatan LIKE ? OR s.nama_lengkap LIKE ?)";
            $search_param = "%$search%";
            $params = array_merge($params, [$search_param, $search_param, $search_param]);
            $types .= 'sss';
        }
        
        if (!empty($filter_jenis)) {
            $where_conditions[] = "c.jenis_catatan = ?";
            $params[] = $filter_jenis;
            $types .= 's';
        }
        
        if (!empty($filter_status)) {
            $where_conditions[] = "c.status = ?";
            $params[] = $filter_status;
            $types .= 's';
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $sql = "
            SELECT c.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as created_by_name
            FROM catatan_siswa c
            JOIN siswa s ON c.siswa_nisn = s.nisn
            LEFT JOIN kelas k ON s.kelas_id = k.id
            JOIN users u ON c.created_by = u.id
            $where_clause
            ORDER BY c.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        $types .= 'ii';
        
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        
        return $stmt->get_result();
    }
    
    // Get total count for pagination
    public function getTotalCount($search = '', $filter_jenis = '', $filter_status = '') {
        $where_conditions = [];
        $params = [];
        $types = '';
        
        if (!empty($search)) {
            $where_conditions[] = "(c.judul_catatan LIKE ? OR c.isi_catatan LIKE ? OR s.nama_lengkap LIKE ?)";
            $search_param = "%$search%";
            $params = array_merge($params, [$search_param, $search_param, $search_param]);
            $types .= 'sss';
        }
        
        if (!empty($filter_jenis)) {
            $where_conditions[] = "c.jenis_catatan = ?";
            $params[] = $filter_jenis;
            $types .= 's';
        }
        
        if (!empty($filter_status)) {
            $where_conditions[] = "c.status = ?";
            $params[] = $filter_status;
            $types .= 's';
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $sql = "
            SELECT COUNT(*) as total
            FROM catatan_siswa c
            JOIN siswa s ON c.siswa_nisn = s.nisn
            $where_clause
        ";
        
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        return $row['total'];
    }
    
    // Get catatan by ID
    public function getCatatanById($id) {
        $stmt = $this->conn->prepare("
            SELECT c.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as created_by_name
            FROM catatan_siswa c
            JOIN siswa s ON c.siswa_nisn = s.nisn
            LEFT JOIN kelas k ON s.kelas_id = k.id
            JOIN users u ON c.created_by = u.id
            WHERE c.id = ?
        ");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        
        return $stmt->get_result()->fetch_assoc();
    }
    
    // Get catatan by siswa NISN
    public function getCatatanBySiswa($siswa_nisn) {
        $stmt = $this->conn->prepare("
            SELECT c.*, u.username as created_by_name
            FROM catatan_siswa c
            JOIN users u ON c.created_by = u.id
            WHERE c.siswa_nisn = ?
            ORDER BY c.created_at DESC
        ");
        $stmt->bind_param("s", $siswa_nisn);
        $stmt->execute();
        
        return $stmt->get_result();
    }
    
    // Get statistik catatan
    public function getStatistikCatatan($siswa_nisn = null) {
        $where_clause = $siswa_nisn ? "WHERE siswa_nisn = ?" : "";
        
        $sql = "
            SELECT 
                COUNT(*) as total_catatan,
                SUM(CASE WHEN status = 'Aktif' THEN 1 ELSE 0 END) as aktif,
                SUM(CASE WHEN status = 'Selesai' THEN 1 ELSE 0 END) as selesai,
                SUM(CASE WHEN status = 'Ditunda' THEN 1 ELSE 0 END) as ditunda,
                SUM(CASE WHEN status = 'Dibatalkan' THEN 1 ELSE 0 END) as dibatalkan,
                SUM(CASE WHEN tingkat_prioritas = 'Tinggi' THEN 1 ELSE 0 END) as prioritas_tinggi,
                SUM(CASE WHEN tingkat_prioritas = 'Sangat Tinggi' THEN 1 ELSE 0 END) as prioritas_sangat_tinggi
            FROM catatan_siswa 
            $where_clause
        ";
        
        if ($siswa_nisn) {
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("s", $siswa_nisn);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $result = $this->conn->query($sql);
        }
        
        return $result->fetch_assoc();
    }
    
    // Get jenis catatan options
    public function getJenisCatatanOptions() {
        $result = $this->conn->query("SHOW COLUMNS FROM catatan_siswa LIKE 'jenis_catatan'");
        $row = $result->fetch_assoc();
        $enum_string = $row['Type'];
        
        // Extract enum values
        preg_match_all("/'([^']+)'/", $enum_string, $matches);
        return $matches[1];
    }
    
    // Get recent catatan
    public function getRecentCatatan($limit = 5) {
        $stmt = $this->conn->prepare("
            SELECT c.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as created_by_name
            FROM catatan_siswa c
            JOIN siswa s ON c.siswa_nisn = s.nisn
            LEFT JOIN kelas k ON s.kelas_id = k.id
            JOIN users u ON c.created_by = u.id
            ORDER BY c.created_at DESC
            LIMIT ?
        ");
        $stmt->bind_param("i", $limit);
        $stmt->execute();
        
        return $stmt->get_result();
    }
    
    // Get catatan yang perlu tindak lanjut
    public function getCatatanPerluTindakLanjut() {
        $stmt = $this->conn->prepare("
            SELECT c.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as created_by_name
            FROM catatan_siswa c
            JOIN siswa s ON c.siswa_nisn = s.nisn
            LEFT JOIN kelas k ON s.kelas_id = k.id
            JOIN users u ON c.created_by = u.id
            WHERE c.status = 'Aktif' 
            AND c.tanggal_tindak_lanjut IS NOT NULL 
            AND c.tanggal_tindak_lanjut <= CURDATE()
            ORDER BY c.tanggal_tindak_lanjut ASC
        ");
        $stmt->execute();
        
        return $stmt->get_result();
    }
}
?>
