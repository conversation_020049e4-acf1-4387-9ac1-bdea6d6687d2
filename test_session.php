<?php
require_once "config/session_helper.php";

echo "<h2>🔍 Session Test</h2>";

echo "<h3>Session Information:</h3>";
$session_info = getSessionInfo();
foreach ($session_info as $key => $value) {
    echo "<strong>" . ucfirst(str_replace("_", " ", $key)) . ":</strong> " . ($value ?? "Not set") . "<br>";
}

echo "<h3>Session Functions Test:</h3>";
echo "<strong>Is Logged In:</strong> " . (isLoggedIn() ? "Yes" : "No") . "<br>";
echo "<strong>User Role:</strong> " . getUserRole() . "<br>";
echo "<strong>Username:</strong> " . getUsername() . "<br>";
echo "<strong>User ID:</strong> " . getUserId() . "<br>";

echo "<h3>Role Check Test:</h3>";
echo "<strong>Has Admin Role:</strong> " . (hasRole("admin") ? "Yes" : "No") . "<br>";
echo "<strong>Has User Role:</strong> " . (hasRole("user") ? "Yes" : "No") . "<br>";

echo "<h3>CSRF Token:</h3>";
echo "<strong>CSRF Token:</strong> " . generateCSRFToken() . "<br>";

echo "<hr>";
echo "<h3>Quick Links:</h3>";
echo "<a href=\"views/dashboard/index.php\">Dashboard</a> | ";
echo "<a href=\"views/siswa/index.php\">Data Siswa</a> | ";
echo "<a href=\"views/siswa/detail.php?nisn=0076116641\">Detail Siswa</a>";
?>