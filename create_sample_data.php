<?php
require_once 'config/db.php';
require_once 'config/functions.php';

echo "=== MEMBUAT DATA SAMPLE ===\n";

// 1. Buat user admin jika belum ada
$check_user = $conn->query("SELECT id FROM users WHERE username = 'admin'");
if ($check_user->num_rows == 0) {
    $password_hash = hashPassword('admin123');
    $stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $username, $password_hash, $role);
    
    $username = 'admin';
    $role = 'admin';
    
    if ($stmt->execute()) {
        echo "✓ User admin berhasil dibuat (username: admin, password: admin123)\n";
    } else {
        echo "✗ Error membuat user admin: " . $conn->error . "\n";
    }
} else {
    echo "User admin sudah ada\n";
}

// 2. Buat kelas sample dengan kurikulum dan tingkat yang benar
$kelas_data = [
    ['KPP', 'Kelas KPP (Kelas Persiapan Pertama)', 'Kurikulum Seminari', '2024/2025', 25, 'Rm. Antonius Widodo, Pr.'],
    ['X', 'Kelas X', 'Kurikulum Merdeka', '2024/2025', 30, 'Y. Guntur Cahyo Dewantoro, ST.'],
    ['X', 'Kelas X-A', 'Kurikulum 13', '2024/2025', 30, 'Dra. Siti Aminah, M.Pd.'],
    ['XI', 'Kelas XI', 'Kurikulum Merdeka', '2024/2025', 28, 'Ahmad Fauzi, S.Pd.'],
    ['XI', 'Kelas XI-A', 'Kurikulum Deep Learning', '2024/2025', 25, 'Dr. Bambang Sutrisno, M.Kom.'],
    ['XII', 'Kelas XII', 'Kurikulum Merdeka', '2024/2025', 27, 'Drs. Haryanto, M.Pd.'],
    ['XII', 'Kelas XII-A', 'Kurikulum Deep Learning', '2024/2025', 26, 'Prof. Dr. Sari Indah, M.T.'],
    ['KPA', 'Kelas KPA (Kelas Persiapan Atas)', 'Kurikulum Seminari', '2024/2025', 20, 'Rm. Yohanes Baptista, Pr.']
];

echo "\nMembuat kelas sample...\n";
foreach ($kelas_data as $kelas) {
    $check_kelas = $conn->prepare("SELECT id FROM kelas WHERE nama_kelas = ?");
    if (!$check_kelas) {
        echo "✗ Error preparing statement: " . $conn->error . "\n";
        continue;
    }

    $check_kelas->bind_param("s", $kelas[1]);
    $check_kelas->execute();

    if ($check_kelas->get_result()->num_rows == 0) {
        $stmt = $conn->prepare("INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, kapasitas, wali_kelas) VALUES (?, ?, ?, ?, ?, ?)");
        if (!$stmt) {
            echo "✗ Error preparing insert statement: " . $conn->error . "\n";
            continue;
        }

        $stmt->bind_param("ssssss", $kelas[1], $kelas[0], $kelas[2], $kelas[3], $kelas[4], $kelas[5]);

        if ($stmt->execute()) {
            echo "✓ Kelas {$kelas[1]} ({$kelas[0]}) berhasil dibuat\n";
        } else {
            echo "✗ Error membuat kelas {$kelas[1]}: " . $conn->error . "\n";
        }
    } else {
        echo "Kelas {$kelas[1]} sudah ada\n";
    }
}

// 3. Buat siswa sample
$siswa_data = [
    [
        'nisn' => '0076116641',
        'nama_lengkap' => 'Ahmad Rizki Pratama',
        'tempat_lahir' => 'Gunungkidul',
        'tanggal_lahir' => '2007-08-08',
        'jenis_kelamin' => 'L',
        'golongan_darah' => 'O',
        'agama' => 'Islam',
        'alamat' => 'Jl. Raya Wonosari No. 123',
        'email' => '<EMAIL>',
        'no_telepon' => '081234567890',
        'kelas_id' => 1,
        'nik' => '3403070808070001',
        'no_kk' => '3403071234567890'
    ],
    [
        'nisn' => '0076116642',
        'nama_lengkap' => 'Siti Nurhaliza',
        'tempat_lahir' => 'Gunungkidul',
        'tanggal_lahir' => '2007-05-15',
        'jenis_kelamin' => 'P',
        'golongan_darah' => 'A',
        'agama' => 'Islam',
        'alamat' => 'Jl. Pemuda No. 45',
        'email' => '<EMAIL>',
        'no_telepon' => '081234567891',
        'kelas_id' => 1,
        'nik' => '3403070515070002',
        'no_kk' => '3403071234567891'
    ],
    [
        'nisn' => '0076116643',
        'nama_lengkap' => 'Budi Santoso',
        'tempat_lahir' => 'Gunungkidul',
        'tanggal_lahir' => '2007-03-20',
        'jenis_kelamin' => 'L',
        'golongan_darah' => 'B',
        'agama' => 'Islam',
        'alamat' => 'Jl. Merdeka No. 67',
        'email' => '<EMAIL>',
        'no_telepon' => '081234567892',
        'kelas_id' => 2,
        'nik' => '3403070320070003',
        'no_kk' => '3403071234567892'
    ]
];

echo "\nMembuat siswa sample...\n";
foreach ($siswa_data as $siswa) {
    $check_siswa = $conn->prepare("SELECT nisn FROM siswa WHERE nisn = ?");
    $check_siswa->bind_param("s", $siswa['nisn']);
    $check_siswa->execute();
    
    if ($check_siswa->get_result()->num_rows == 0) {
        $stmt = $conn->prepare("
            INSERT INTO siswa 
            (nisn, nik, no_kk, nama_lengkap, tempat_lahir, tanggal_lahir, jenis_kelamin, 
             golongan_darah, agama, alamat, email, no_telepon, kelas_id, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Aktif')
        ");
        $stmt->bind_param("ssssssssssssi", 
            $siswa['nisn'], $siswa['nik'], $siswa['no_kk'], $siswa['nama_lengkap'],
            $siswa['tempat_lahir'], $siswa['tanggal_lahir'], $siswa['jenis_kelamin'],
            $siswa['golongan_darah'], $siswa['agama'], $siswa['alamat'],
            $siswa['email'], $siswa['no_telepon'], $siswa['kelas_id']
        );
        
        if ($stmt->execute()) {
            echo "✓ Siswa {$siswa['nama_lengkap']} berhasil dibuat\n";
        } else {
            echo "✗ Error membuat siswa {$siswa['nama_lengkap']}: " . $conn->error . "\n";
        }
    } else {
        echo "Siswa {$siswa['nama_lengkap']} sudah ada\n";
    }
}

// 4. Buat data absensi sample
echo "\nMembuat data absensi sample...\n";
$absensi_data = [
    ['0076116641', '2025-07-01', 'Hadir', null],
    ['0076116641', '2025-07-02', 'Hadir', null],
    ['0076116641', '2025-07-03', 'Sakit', 'Demam'],
    ['0076116641', '2025-07-04', 'Hadir', null],
    ['0076116641', '2025-07-05', 'Hadir', null],
    ['0076116642', '2025-07-01', 'Hadir', null],
    ['0076116642', '2025-07-02', 'Izin', 'Keperluan keluarga'],
    ['0076116642', '2025-07-03', 'Hadir', null],
    ['0076116642', '2025-07-04', 'Hadir', null],
    ['0076116642', '2025-07-05', 'Hadir', null],
    ['0076116643', '2025-07-01', 'Hadir', null],
    ['0076116643', '2025-07-02', 'Hadir', null],
    ['0076116643', '2025-07-03', 'Hadir', null],
    ['0076116643', '2025-07-04', 'Alpa', null],
    ['0076116643', '2025-07-05', 'Hadir', null]
];

foreach ($absensi_data as $absensi) {
    $check_absensi = $conn->prepare("SELECT id FROM absensi WHERE siswa_nisn = ? AND tanggal = ?");
    $check_absensi->bind_param("ss", $absensi[0], $absensi[1]);
    $check_absensi->execute();
    
    if ($check_absensi->get_result()->num_rows == 0) {
        $stmt = $conn->prepare("INSERT INTO absensi (siswa_nisn, tanggal, keterangan, catatan) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $absensi[0], $absensi[1], $absensi[2], $absensi[3]);
        
        if ($stmt->execute()) {
            echo "✓ Absensi {$absensi[0]} - {$absensi[1]} berhasil dibuat\n";
        } else {
            echo "✗ Error membuat absensi: " . $conn->error . "\n";
        }
    }
}

// 5. Buat catatan sample
echo "\nMembuat catatan sample...\n";
$catatan_data = [
    [
        'siswa_nisn' => '0076116641',
        'jenis_catatan' => 'Catatan Wali Kelas X',
        'judul_catatan' => 'Prestasi Akademik Baik',
        'isi_catatan' => 'Ahmad menunjukkan prestasi akademik yang baik di semester ini. Aktif dalam diskusi kelas dan selalu mengerjakan tugas tepat waktu.',
        'tingkat_prioritas' => 'Sedang',
        'status' => 'Aktif',
        'created_by' => 1
    ],
    [
        'siswa_nisn' => '0076116642',
        'jenis_catatan' => 'Konseling BK',
        'judul_catatan' => 'Konsultasi Pemilihan Jurusan',
        'isi_catatan' => 'Siti berkonsultasi mengenai pemilihan jurusan untuk melanjutkan ke perguruan tinggi. Menunjukkan minat di bidang kesehatan.',
        'tingkat_prioritas' => 'Tinggi',
        'status' => 'Selesai',
        'tindak_lanjut' => 'Memberikan informasi universitas dengan jurusan kesehatan',
        'created_by' => 1
    ],
    [
        'siswa_nisn' => '0076116643',
        'jenis_catatan' => 'Pelanggaran',
        'judul_catatan' => 'Terlambat Masuk Kelas',
        'isi_catatan' => 'Budi terlambat masuk kelas sebanyak 3 kali dalam minggu ini. Perlu diberi peringatan dan pembinaan.',
        'tingkat_prioritas' => 'Tinggi',
        'status' => 'Aktif',
        'tindak_lanjut' => 'Panggil orang tua untuk konsultasi',
        'tanggal_tindak_lanjut' => '2025-07-10',
        'created_by' => 1
    ]
];

foreach ($catatan_data as $catatan) {
    $stmt = $conn->prepare("
        INSERT INTO catatan_siswa 
        (siswa_nisn, jenis_catatan, judul_catatan, isi_catatan, tingkat_prioritas, status, tindak_lanjut, tanggal_tindak_lanjut, created_by) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $tindak_lanjut = $catatan['tindak_lanjut'] ?? null;
    $tanggal_tindak_lanjut = $catatan['tanggal_tindak_lanjut'] ?? null;

    $stmt->bind_param("ssssssssi",
        $catatan['siswa_nisn'], $catatan['jenis_catatan'], $catatan['judul_catatan'],
        $catatan['isi_catatan'], $catatan['tingkat_prioritas'], $catatan['status'],
        $tindak_lanjut, $tanggal_tindak_lanjut, $catatan['created_by']
    );
    
    if ($stmt->execute()) {
        echo "✓ Catatan '{$catatan['judul_catatan']}' berhasil dibuat\n";
    } else {
        echo "✗ Error membuat catatan: " . $conn->error . "\n";
    }
}

echo "\n🎉 Data sample berhasil dibuat!\n";
echo "\nInformasi Login:\n";
echo "Username: admin\n";
echo "Password: admin123\n";
echo "\nURL Aplikasi: http://localhost:8000\n";

$conn->close();
?>
