<?php
/**
 * Test script untuk fungsi edit siswa
 */

require_once 'config/session_helper.php';
require_once 'config/db.php';

echo "<h2>🧪 Test Edit Siswa Function</h2>";

// Test 1: Cek koneksi database
echo "<h3>1. Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Test 2: Cek data siswa yang akan diedit
echo "<h3>2. Sample Student Data</h3>";
$test_nisn = '0076116641';
$stmt = $conn->prepare("SELECT * FROM siswa WHERE nisn = ?");
$stmt->bind_param("s", $test_nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if ($siswa) {
    echo "✅ Student found: <strong>" . htmlspecialchars($siswa['nama_lengkap']) . "</strong><br>";
    echo "📋 Current data:<br>";
    echo "- NISN: " . $siswa['nisn'] . "<br>";
    echo "- Nama: " . htmlspecialchars($siswa['nama_lengkap']) . "<br>";
    echo "- Email: " . ($siswa['email'] ?? 'Not set') . "<br>";
    echo "- Status: " . $siswa['status'] . "<br>";
} else {
    echo "❌ Student not found<br>";
    exit;
}

// Test 3: Cek file edit form
echo "<h3>3. Edit Form Files</h3>";
$files_to_check = [
    'views/siswa/edit.php',
    'controllers/SiswaController.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ File exists: <strong>$file</strong><br>";
    } else {
        echo "❌ File missing: <strong>$file</strong><br>";
    }
}

// Test 4: Cek form action di edit.php
echo "<h3>4. Form Configuration Check</h3>";
$edit_content = file_get_contents('views/siswa/edit.php');

if (strpos($edit_content, 'name="edit_siswa"') !== false) {
    echo "✅ Form has correct action name: <strong>edit_siswa</strong><br>";
} else {
    echo "❌ Form missing edit_siswa action<br>";
}

if (strpos($edit_content, 'action="../../controllers/SiswaController.php"') !== false) {
    echo "✅ Form action points to correct controller<br>";
} else {
    echo "❌ Form action incorrect<br>";
}

if (strpos($edit_content, 'method="POST"') !== false) {
    echo "✅ Form uses POST method<br>";
} else {
    echo "❌ Form method incorrect<br>";
}

// Test 5: Cek controller handler
echo "<h3>5. Controller Handler Check</h3>";
$controller_content = file_get_contents('controllers/SiswaController.php');

if (strpos($controller_content, "if (isset(\$_POST['edit_siswa']))") !== false) {
    echo "✅ Controller has edit_siswa handler<br>";
} else {
    echo "❌ Controller missing edit_siswa handler<br>";
}

if (strpos($controller_content, 'UPDATE siswa SET') !== false) {
    echo "✅ Controller has UPDATE query<br>";
} else {
    echo "❌ Controller missing UPDATE query<br>";
}

// Test 6: Simulate form submission (dry run)
echo "<h3>6. Form Submission Simulation</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Simulated POST data:</strong><br>";
$simulated_post = [
    'edit_siswa' => '1',
    'nisn' => $test_nisn,
    'nama_lengkap' => $siswa['nama_lengkap'],
    'email' => '<EMAIL>',
    'status' => 'Aktif'
];

foreach ($simulated_post as $key => $value) {
    echo "- $key: $value<br>";
}
echo "</div>";

// Test 7: Check redirect URLs
echo "<h3>7. Redirect URLs Check</h3>";
if (strpos($controller_content, 'detail.php?nisn=') !== false) {
    echo "✅ Success redirect to detail page<br>";
} else {
    echo "❌ Success redirect missing or incorrect<br>";
}

if (strpos($controller_content, 'edit.php?nisn=') !== false) {
    echo "✅ Error redirect to edit page<br>";
} else {
    echo "❌ Error redirect missing or incorrect<br>";
}

// Test 8: Check session handling
echo "<h3>8. Session Handling</h3>";
if (strpos($controller_content, 'session_helper.php') !== false) {
    echo "✅ Uses session helper<br>";
} else {
    echo "❌ Missing session helper<br>";
}

echo "<hr>";
echo "<h3>🔗 Test Links</h3>";
echo "<p><a href='views/siswa/edit.php?nisn=$test_nisn' target='_blank'>📝 Edit Form</a></p>";
echo "<p><a href='views/siswa/detail.php?nisn=$test_nisn' target='_blank'>👁️ Detail Page</a></p>";
echo "<p><a href='views/siswa/index.php' target='_blank'>📋 Student List</a></p>";

echo "<hr>";
echo "<h3>📋 Manual Test Steps</h3>";
echo "<ol>";
echo "<li>Open the edit form link above</li>";
echo "<li>Make a small change (e.g., update email)</li>";
echo "<li>Click 'Simpan Perubahan' button</li>";
echo "<li>Should redirect to detail page with success message</li>";
echo "<li>Verify the change was saved</li>";
echo "</ol>";

echo "<hr>";
echo "<h3>🐛 Debugging Tips</h3>";
echo "<ul>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Check network tab for form submission</li>";
echo "<li>Look for PHP errors in server logs</li>";
echo "<li>Verify database connection and permissions</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
