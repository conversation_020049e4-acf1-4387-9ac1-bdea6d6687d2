<?php
require_once 'config/db.php';

echo "=== STRUKTUR DATABASE ===\n";

// Cek tabel yang ada
$result = $conn->query("SHOW TABLES");
echo "Tabel yang ada:\n";
while ($row = $result->fetch_array()) {
    echo "- " . $row[0] . "\n";
}

echo "\n=== STRUKTUR TABEL SISWA ===\n";
$result = $conn->query("DESCRIBE siswa");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
    }
} else {
    echo "Tabel siswa tidak ditemukan\n";
}

echo "\n=== STRUKTUR TABEL ABSENSI ===\n";
$result = $conn->query("DESCRIBE absensi");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
    }
} else {
    echo "Tabel absensi tidak ditemukan\n";
}

echo "\n=== STRUKTUR TABEL BERKAS ===\n";
$result = $conn->query("DESCRIBE berkas");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
    }
} else {
    echo "Tabel berkas tidak ditemukan\n";
}

echo "\n=== STRUKTUR TABEL USERS ===\n";
$result = $conn->query("DESCRIBE users");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
    }
} else {
    echo "Tabel users tidak ditemukan\n";
}

$conn->close();
?>
