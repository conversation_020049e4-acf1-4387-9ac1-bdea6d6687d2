<?php
session_start();
require_once '../../config/db.php';

$nisn = $_GET['nisn'] ?? '';
$query = "SELECT s.*, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.nisn = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    header('Location: index.php');
    exit;
}

// Get daftar kelas
$kelas_result = $conn->query("SELECT * FROM kelas ORDER BY nama_kelas");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Data Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        .btn-save {
            background: #007bff;
            border-color: #007bff;
            padding: 12px 30px;
            font-weight: 500;
        }
        .btn-cancel {
            background: #6c757d;
            border-color: #6c757d;
            padding: 12px 30px;
            font-weight: 500;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link active" href="index.php">Data Siswa</a>
                <a class="nav-link" href="../kelas/index.php">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-edit me-2"></i>Edit Data Siswa</h4>
                <small class="text-muted">Edit data siswa: <?php echo htmlspecialchars($siswa['nama_lengkap']); ?></small>
            </div>
            <a href="detail.php?nisn=<?php echo $siswa['nisn']; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php
                switch($_GET['error']) {
                    case 'database':
                        echo 'Gagal menyimpan perubahan data!';
                        break;
                    case 'upload_failed':
                        echo 'Gagal mengupload foto!';
                        break;
                    default:
                        echo 'Terjadi kesalahan!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Form -->
        <div class="form-container">
            <form action="../../controllers/SiswaController.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="nisn" value="<?php echo $siswa['nisn']; ?>">

                <div class="row">
                    <!-- Data Pribadi -->
                    <div class="col-md-6">
                        <h5 class="section-title"><i class="fas fa-user me-2"></i>Data Pribadi</h5>

                        <div class="mb-3">
                            <label class="form-label">NIS *</label>
                            <input type="text" class="form-control" value="A25.285" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">NISN *</label>
                            <input type="text" class="form-control" name="nisn_display" value="<?php echo htmlspecialchars($siswa['nisn']); ?>" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">NIK</label>
                            <input type="text" class="form-control" name="nik" value="<?php echo htmlspecialchars($siswa['nik'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">No. KK</label>
                            <input type="text" class="form-control" name="no_kk" value="<?php echo htmlspecialchars($siswa['no_kk'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Nama Lengkap *</label>
                            <input type="text" class="form-control" name="nama_lengkap" value="<?php echo htmlspecialchars($siswa['nama_lengkap']); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Jenis Kelamin *</label>
                            <select class="form-select" name="jenis_kelamin" required>
                                <option value="">Pilih Jenis Kelamin</option>
                                <option value="Laki-laki" <?php echo ($siswa['jenis_kelamin'] == 'Laki-laki') ? 'selected' : ''; ?>>Laki-laki</option>
                                <option value="Perempuan" <?php echo ($siswa['jenis_kelamin'] == 'Perempuan') ? 'selected' : ''; ?>>Perempuan</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tempat Lahir</label>
                            <input type="text" class="form-control" name="tempat_lahir" value="<?php echo htmlspecialchars($siswa['tempat_lahir'] ?? 'Gunungkidul'); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tanggal Lahir</label>
                            <input type="date" class="form-control" name="tanggal_lahir" value="<?php echo $siswa['tanggal_lahir'] ?? '2007-08-08'; ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Golongan Darah</label>
                            <select class="form-select" name="golongan_darah">
                                <option value="">Pilih Golongan Darah</option>
                                <option value="A" <?php echo ($siswa['golongan_darah'] == 'A') ? 'selected' : ''; ?>>A</option>
                                <option value="B" <?php echo ($siswa['golongan_darah'] == 'B') ? 'selected' : ''; ?>>B</option>
                                <option value="AB" <?php echo ($siswa['golongan_darah'] == 'AB') ? 'selected' : ''; ?>>AB</option>
                                <option value="O" <?php echo ($siswa['golongan_darah'] == 'O') ? 'selected' : ''; ?>>O</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Agama</label>
                            <select class="form-select" name="agama">
                                <option value="">Pilih Agama</option>
                                <option value="Islam" <?php echo ($siswa['agama'] == 'Islam') ? 'selected' : ''; ?>>Islam</option>
                                <option value="Kristen" <?php echo ($siswa['agama'] == 'Kristen') ? 'selected' : ''; ?>>Kristen</option>
                                <option value="Katolik" <?php echo ($siswa['agama'] == 'Katolik') ? 'selected' : ''; ?>>Katolik</option>
                                <option value="Hindu" <?php echo ($siswa['agama'] == 'Hindu') ? 'selected' : ''; ?>>Hindu</option>
                                <option value="Buddha" <?php echo ($siswa['agama'] == 'Buddha') ? 'selected' : ''; ?>>Buddha</option>
                                <option value="Konghucu" <?php echo ($siswa['agama'] == 'Konghucu') ? 'selected' : ''; ?>>Konghucu</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Alamat Lengkap</label>
                            <textarea class="form-control" name="alamat" rows="3"><?php echo htmlspecialchars($siswa['alamat'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Data Akademik & Kontak -->
                    <div class="col-md-6">
                        <h5 class="section-title"><i class="fas fa-graduation-cap me-2"></i>Data Akademik & Kontak</h5>

                        <div class="mb-3">
                            <label class="form-label">Kelas *</label>
                            <select class="form-select" name="kelas_id" required>
                                <option value="">Pilih Kelas</option>
                                <?php while ($kelas = $kelas_result->fetch_assoc()): ?>
                                <option value="<?php echo $kelas['id']; ?>" <?php echo ($siswa['kelas_id'] == $kelas['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($kelas['nama_kelas']); ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Status Siswa</label>
                            <select class="form-select" name="status">
                                <option value="Aktif" <?php echo ($siswa['status'] == 'Aktif') ? 'selected' : ''; ?>>Aktif</option>
                                <option value="Tidak Aktif" <?php echo ($siswa['status'] == 'Tidak Aktif') ? 'selected' : ''; ?>>Tidak Aktif</option>
                                <option value="Lulus" <?php echo ($siswa['status'] == 'Lulus') ? 'selected' : ''; ?>>Lulus</option>
                                <option value="Pindah" <?php echo ($siswa['status'] == 'Pindah') ? 'selected' : ''; ?>>Pindah</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tahun Masuk</label>
                            <input type="number" class="form-control" name="tahun_masuk" value="<?php echo $siswa['tahun_masuk'] ?? date('Y'); ?>" min="2000" max="<?php echo date('Y'); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Nama Ayah</label>
                            <input type="text" class="form-control" name="nama_ayah" value="<?php echo htmlspecialchars($siswa['nama_ayah'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Nama Ibu</label>
                            <input type="text" class="form-control" name="nama_ibu" value="<?php echo htmlspecialchars($siswa['nama_ibu'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Pekerjaan Ayah</label>
                            <input type="text" class="form-control" name="pekerjaan_ayah" value="<?php echo htmlspecialchars($siswa['pekerjaan_ayah'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Pekerjaan Ibu</label>
                            <input type="text" class="form-control" name="pekerjaan_ibu" value="<?php echo htmlspecialchars($siswa['pekerjaan_ibu'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Asal Sekolah & Berkas</label>
                            <input type="text" class="form-control" name="asal_sekolah" value="<?php echo htmlspecialchars($siswa['asal_sekolah'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Asal Pasal</label>
                            <input type="text" class="form-control" name="asal_pasal" value="<?php echo htmlspecialchars($siswa['asal_pasal'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Alamat</label>
                            <textarea class="form-control" name="alamat_lengkap" rows="3"><?php echo htmlspecialchars($siswa['alamat_lengkap'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="detail.php?nisn=<?php echo $siswa['nisn']; ?>" class="btn btn-cancel text-white">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-save text-white">
                                <i class="fas fa-save me-2"></i>Simpan Perubahan
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>