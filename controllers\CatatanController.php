<?php
require_once '../config/db.php';
require_once '../config/functions.php';
session_start();

// Tambah Catatan Siswa
if (isset($_POST['tambah_catatan'])) {
    $siswa_nisn = $_POST['siswa_nisn'];
    $jenis_catatan = $_POST['jenis_catatan'];
    $judul_catatan = $_POST['judul_catatan'];
    $isi_catatan = $_POST['isi_catatan'];
    $tingkat_prioritas = $_POST['tingkat_prioritas'];
    $status = $_POST['status'];
    $tindak_lanjut = $_POST['tindak_lanjut'] ?? null;
    $tanggal_tindak_lanjut = !empty($_POST['tanggal_tindak_lanjut']) ? $_POST['tanggal_tindak_lanjut'] : null;
    $created_by = $_SESSION['user_id'];

    $stmt = $conn->prepare("INSERT INTO catatan_siswa (siswa_nisn, jenis_catatan, judul_catatan, isi_catatan, tingkat_prioritas, status, tindak_lanjut, tanggal_tindak_lanjut, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssssssi", $siswa_nisn, $jenis_catatan, $judul_catatan, $isi_catatan, $tingkat_prioritas, $status, $tindak_lanjut, $tanggal_tindak_lanjut, $created_by);
    
    if ($stmt->execute()) {
        header("Location: ../views/catatan/index.php?success=1");
    } else {
        header("Location: ../views/catatan/tambah.php?error=1");
    }
    exit;
}

// Edit Catatan Siswa
if (isset($_POST['edit_catatan'])) {
    $id = $_POST['id'];
    $jenis_catatan = $_POST['jenis_catatan'];
    $judul_catatan = $_POST['judul_catatan'];
    $isi_catatan = $_POST['isi_catatan'];
    $tingkat_prioritas = $_POST['tingkat_prioritas'];
    $status = $_POST['status'];
    $tindak_lanjut = $_POST['tindak_lanjut'] ?? null;
    $tanggal_tindak_lanjut = !empty($_POST['tanggal_tindak_lanjut']) ? $_POST['tanggal_tindak_lanjut'] : null;

    $stmt = $conn->prepare("UPDATE catatan_siswa SET jenis_catatan=?, judul_catatan=?, isi_catatan=?, tingkat_prioritas=?, status=?, tindak_lanjut=?, tanggal_tindak_lanjut=? WHERE id=?");
    $stmt->bind_param("sssssssi", $jenis_catatan, $judul_catatan, $isi_catatan, $tingkat_prioritas, $status, $tindak_lanjut, $tanggal_tindak_lanjut, $id);
    
    if ($stmt->execute()) {
        header("Location: ../views/catatan/index.php?updated=1");
    } else {
        header("Location: ../views/catatan/edit.php?id=$id&error=1");
    }
    exit;
}

// Hapus Catatan
if (isset($_GET['hapus'])) {
    $id = $_GET['hapus'];
    $stmt = $conn->prepare("DELETE FROM catatan_siswa WHERE id = ?");
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        header("Location: ../views/catatan/index.php?deleted=1");
    } else {
        header("Location: ../views/catatan/index.php?error=1");
    }
    exit;
}

// Get Catatan by Siswa (AJAX)
if (isset($_GET['get_catatan_siswa'])) {
    $siswa_nisn = $_GET['siswa_nisn'];
    
    $stmt = $conn->prepare("
        SELECT c.*, s.nama_lengkap, u.username as created_by_name 
        FROM catatan_siswa c 
        JOIN siswa s ON c.siswa_nisn = s.nisn 
        JOIN users u ON c.created_by = u.id 
        WHERE c.siswa_nisn = ? 
        ORDER BY c.created_at DESC
    ");
    $stmt->bind_param("s", $siswa_nisn);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $catatan = [];
    while ($row = $result->fetch_assoc()) {
        $catatan[] = $row;
    }
    
    header('Content-Type: application/json');
    echo json_encode($catatan);
    exit;
}

// Get Statistik Catatan (AJAX)
if (isset($_GET['get_statistik_catatan'])) {
    $siswa_nisn = $_GET['siswa_nisn'] ?? null;
    
    $where_clause = $siswa_nisn ? "WHERE siswa_nisn = ?" : "";
    
    $sql = "
        SELECT 
            COUNT(*) as total_catatan,
            SUM(CASE WHEN status = 'Aktif' THEN 1 ELSE 0 END) as aktif,
            SUM(CASE WHEN status = 'Selesai' THEN 1 ELSE 0 END) as selesai,
            SUM(CASE WHEN tingkat_prioritas = 'Tinggi' OR tingkat_prioritas = 'Sangat Tinggi' THEN 1 ELSE 0 END) as prioritas_tinggi
        FROM catatan_siswa 
        $where_clause
    ";
    
    if ($siswa_nisn) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $siswa_nisn);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $conn->query($sql);
    }
    
    $stats = $result->fetch_assoc();
    
    header('Content-Type: application/json');
    echo json_encode($stats);
    exit;
}

// Update Status Catatan (AJAX)
if (isset($_POST['update_status_catatan'])) {
    $id = $_POST['id'];
    $status = $_POST['status'];
    
    $stmt = $conn->prepare("UPDATE catatan_siswa SET status = ? WHERE id = ?");
    $stmt->bind_param("si", $status, $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Status berhasil diupdate']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal update status']);
    }
    exit;
}
?>
