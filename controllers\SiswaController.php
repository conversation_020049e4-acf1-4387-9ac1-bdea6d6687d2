<?php
require_once '../config/db.php';
require_once '../config/functions.php';
require_once '../config/session_helper.php';

// Pastikan user sudah login
requireLogin(true);

// Enable error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Tambah Siswa
if (isset($_POST['tambah_siswa'])) {
    $nisn = $_POST['nisn'];
    $nik = $_POST['nik'] ?? null;
    $no_kk = $_POST['no_kk'] ?? null;
    $nama_lengkap = $_POST['nama_lengkap'];
    $tempat_lahir = $_POST['tempat_lahir'] ?? null;
    $tanggal_lahir = !empty($_POST['tanggal_lahir']) ? $_POST['tanggal_lahir'] : null;
    $jenis_kelamin = $_POST['jenis_kelamin'] ?? null;
    $golongan_darah = $_POST['golongan_darah'] ?? null;
    $agama = $_POST['agama'] ?? null;
    $alamat = $_POST['alamat'] ?? null;
    $nama_ayah = $_POST['nama_ayah'] ?? null;
    $nama_ibu = $_POST['nama_ibu'] ?? null;
    $pekerjaan_ayah = $_POST['pekerjaan_ayah'] ?? null;
    $pekerjaan_ibu = $_POST['pekerjaan_ibu'] ?? null;
    $email = $_POST['email'] ?? null;
    $no_telepon = $_POST['no_telepon'] ?? null;
    $no_telepon_ortu = $_POST['no_telepon_ortu'] ?? null;
    $kelas_id = !empty($_POST['kelas_id']) ? $_POST['kelas_id'] : null;
    $status = $_POST['status'] ?? 'Aktif';

    // Cek apakah NISN sudah ada
    $check_stmt = $conn->prepare("SELECT nisn FROM siswa WHERE nisn = ?");
    $check_stmt->bind_param("s", $nisn);
    $check_stmt->execute();
    $existing = $check_stmt->get_result()->fetch_assoc();

    if ($existing) {
        header("Location: ../views/siswa/tambah.php?error=nisn_exists");
        exit;
    }

    // Upload foto jika ada
    $foto = null;
    if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
        $foto = uploadFile($_FILES['foto'], 'foto');
        if (!$foto) {
            header("Location: ../views/siswa/tambah.php?error=upload_failed");
            exit;
        }
    }

    $stmt = $conn->prepare("
        INSERT INTO siswa
        (nisn, nik, no_kk, nama_lengkap, tempat_lahir, tanggal_lahir, jenis_kelamin,
         golongan_darah, agama, alamat, nama_ayah, nama_ibu, pekerjaan_ayah, pekerjaan_ibu,
         email, no_telepon, no_telepon_ortu, kelas_id, foto, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->bind_param("ssssssssssssssssssss",
        $nisn, $nik, $no_kk, $nama_lengkap, $tempat_lahir, $tanggal_lahir, $jenis_kelamin,
        $golongan_darah, $agama, $alamat, $nama_ayah, $nama_ibu, $pekerjaan_ayah, $pekerjaan_ibu,
        $email, $no_telepon, $no_telepon_ortu, $kelas_id, $foto, $status
    );

    if ($stmt->execute()) {
        header("Location: ../views/siswa/index.php?success=1");
    } else {
        // Hapus foto jika gagal insert
        if ($foto && file_exists("../uploads/foto/$foto")) {
            unlink("../uploads/foto/$foto");
        }
        header("Location: ../views/siswa/tambah.php?error=database");
    }
    exit;
}

// Edit Siswa
if (isset($_POST['edit_siswa'])) {
    // Debug: Log received data
    error_log("Edit siswa request received for NISN: " . ($_POST['nisn'] ?? 'not set'));

    $nisn = $_POST['nisn'];
    $nik = $_POST['nik'] ?? null;
    $no_kk = $_POST['no_kk'] ?? null;
    $nama_lengkap = $_POST['nama_lengkap'];
    $tempat_lahir = $_POST['tempat_lahir'] ?? null;
    $tanggal_lahir = !empty($_POST['tanggal_lahir']) ? $_POST['tanggal_lahir'] : null;
    $jenis_kelamin = $_POST['jenis_kelamin'] ?? null;
    $golongan_darah = $_POST['golongan_darah'] ?? null;
    $agama = $_POST['agama'] ?? null;
    $alamat = $_POST['alamat'] ?? null;
    $nama_ayah = $_POST['nama_ayah'] ?? null;
    $nama_ibu = $_POST['nama_ibu'] ?? null;
    $pekerjaan_ayah = $_POST['pekerjaan_ayah'] ?? null;
    $pekerjaan_ibu = $_POST['pekerjaan_ibu'] ?? null;
    $email = $_POST['email'] ?? null;
    $no_telepon = $_POST['no_telepon'] ?? null;
    $no_telepon_ortu = $_POST['no_telepon_ortu'] ?? null;
    $kelas_id = !empty($_POST['kelas_id']) ? $_POST['kelas_id'] : null;
    $status = $_POST['status'] ?? 'Aktif';

    // Get current photo
    $current_stmt = $conn->prepare("SELECT foto FROM siswa WHERE nisn = ?");
    $current_stmt->bind_param("s", $nisn);
    $current_stmt->execute();
    $current_data = $current_stmt->get_result()->fetch_assoc();
    $foto = $current_data['foto'];

    // Upload foto baru jika ada
    if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
        $new_foto = uploadFile($_FILES['foto'], 'foto');
        if ($new_foto) {
            // Hapus foto lama
            if ($foto && file_exists("../uploads/foto/$foto")) {
                unlink("../uploads/foto/$foto");
            }
            $foto = $new_foto;
        }
    }

    $stmt = $conn->prepare("
        UPDATE siswa SET
        nik=?, no_kk=?, nama_lengkap=?, tempat_lahir=?, tanggal_lahir=?, jenis_kelamin=?,
        golongan_darah=?, agama=?, alamat=?, nama_ayah=?, nama_ibu=?, pekerjaan_ayah=?, pekerjaan_ibu=?,
        email=?, no_telepon=?, no_telepon_ortu=?, kelas_id=?, foto=?, status=?
        WHERE nisn=?
    ");

    $stmt->bind_param("ssssssssssssssssssss",
        $nik, $no_kk, $nama_lengkap, $tempat_lahir, $tanggal_lahir, $jenis_kelamin,
        $golongan_darah, $agama, $alamat, $nama_ayah, $nama_ibu, $pekerjaan_ayah, $pekerjaan_ibu,
        $email, $no_telepon, $no_telepon_ortu, $kelas_id, $foto, $status, $nisn
    );

    if ($stmt->execute()) {
        error_log("Siswa updated successfully: " . $nisn);
        header("Location: ../views/siswa/detail.php?nisn=$nisn&updated=1");
    } else {
        error_log("Failed to update siswa: " . $conn->error);
        header("Location: ../views/siswa/edit.php?nisn=$nisn&error=database");
    }
    exit;
}

// Hapus Siswa
if (isset($_GET['hapus'])) {
    $nisn = $_GET['hapus'];

    // Cek apakah siswa ada
    $check_stmt = $conn->prepare("SELECT foto FROM siswa WHERE nisn = ?");
    $check_stmt->bind_param("s", $nisn);
    $check_stmt->execute();
    $foto_data = $check_stmt->get_result()->fetch_assoc();

    if (!$foto_data) {
        header("Location: ../views/siswa/index.php?error=not_found");
        exit;
    }

    // Hapus dari database
    $stmt = $conn->prepare("DELETE FROM siswa WHERE nisn = ?");
    $stmt->bind_param("s", $nisn);

    if ($stmt->execute()) {
        // Hapus foto jika ada
        if ($foto_data['foto'] && file_exists("../uploads/foto/" . $foto_data['foto'])) {
            unlink("../uploads/foto/" . $foto_data['foto']);
        }
        header("Location: ../views/siswa/index.php?deleted=1");
    } else {
        header("Location: ../views/siswa/index.php?error=delete_failed");
    }
    exit;
}

// Get Siswa by NISN (AJAX)
if (isset($_GET['get_siswa'])) {
    $nisn = $_GET['nisn'];

    $stmt = $conn->prepare("
        SELECT s.*, k.nama_kelas
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        WHERE s.nisn = ?
    ");
    $stmt->bind_param("s", $nisn);
    $stmt->execute();
    $result = $stmt->get_result();
    $siswa = $result->fetch_assoc();

    header('Content-Type: application/json');
    echo json_encode($siswa ?: []);
    exit;
}

// Search Siswa (AJAX)
if (isset($_GET['search_siswa'])) {
    $search = $_GET['search'];
    $limit = $_GET['limit'] ?? 10;

    $stmt = $conn->prepare("
        SELECT s.nisn, s.nama_lengkap, k.nama_kelas
        FROM siswa s
        LEFT JOIN kelas k ON s.kelas_id = k.id
        WHERE s.nama_lengkap LIKE ? OR s.nisn LIKE ?
        ORDER BY s.nama_lengkap
        LIMIT ?
    ");
    $search_param = "%$search%";
    $stmt->bind_param("ssi", $search_param, $search_param, $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $siswa = [];
    while ($row = $result->fetch_assoc()) {
        $siswa[] = $row;
    }

    header('Content-Type: application/json');
    echo json_encode($siswa);
    exit;
}

// Update Status Siswa (AJAX)
if (isset($_POST['update_status_siswa'])) {
    $nisn = $_POST['nisn'];
    $status = $_POST['status'];

    $stmt = $conn->prepare("UPDATE siswa SET status = ? WHERE nisn = ?");
    $stmt->bind_param("ss", $status, $nisn);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Status berhasil diupdate']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal update status']);
    }
    exit;
}
?>