<?php
require_once 'config/db.php';

echo "=== MENJALANKAN DATABASE UPGRADE ===\n";

// Baca file SQL
$sql_content = file_get_contents('database_upgrade.sql');

// Pisahkan query berdasarkan semicolon
$queries = explode(';', $sql_content);

$success_count = 0;
$error_count = 0;

foreach ($queries as $query) {
    $query = trim($query);
    
    // Skip query kosong atau komentar
    if (empty($query) || strpos($query, '--') === 0) {
        continue;
    }
    
    echo "Menjalankan: " . substr($query, 0, 50) . "...\n";
    
    if ($conn->query($query)) {
        echo "✓ Berhasil\n";
        $success_count++;
    } else {
        echo "✗ Error: " . $conn->error . "\n";
        $error_count++;
    }
    
    echo "---\n";
}

echo "\n=== HASIL UPGRADE ===\n";
echo "Berhasil: $success_count query\n";
echo "Error: $error_count query\n";

if ($error_count == 0) {
    echo "\n🎉 Database upgrade berhasil!\n";
} else {
    echo "\n⚠️ Ada beberapa error, silakan periksa log di atas.\n";
}

// Verifikasi tabel baru
echo "\n=== VERIFIKASI TABEL BARU ===\n";
$result = $conn->query("SHOW TABLES");
echo "Tabel yang tersedia:\n";
while ($row = $result->fetch_array()) {
    echo "- " . $row[0] . "\n";
}

$conn->close();
?>
