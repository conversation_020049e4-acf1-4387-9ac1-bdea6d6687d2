<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit;
}

require_once '../../config/db.php';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$filter_jenis = $_GET['filter_jenis'] ?? '';
$filter_siswa = $_GET['filter_siswa'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(b.nama_file LIKE ? OR s.nama_lengkap LIKE ? OR b.keterangan LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $types .= 'sss';
}

if (!empty($filter_jenis)) {
    $where_conditions[] = "b.jenis_berkas = ?";
    $params[] = $filter_jenis;
    $types .= 's';
}

if (!empty($filter_siswa)) {
    $where_conditions[] = "b.siswa_nisn = ?";
    $params[] = $filter_siswa;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total records
$count_sql = "
    SELECT COUNT(*) as total
    FROM berkas_siswa b
    JOIN siswa s ON b.siswa_nisn = s.nisn
    $where_clause
";

if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
} else {
    $total_records = $conn->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $limit);

// Get berkas data
$sql = "
    SELECT b.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as uploaded_by_name
    FROM berkas_siswa b
    JOIN siswa s ON b.siswa_nisn = s.nisn
    LEFT JOIN kelas k ON s.kelas_id = k.id
    JOIN users u ON b.uploaded_by = u.id
    $where_clause
    ORDER BY b.uploaded_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;
$types .= 'ii';

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$berkas_list = $stmt->get_result();

// Get filter options
$siswa_options = $conn->query("SELECT nisn, nama_lengkap FROM siswa WHERE status = 'Aktif' ORDER BY nama_lengkap");
$jenis_result = $conn->query("SHOW COLUMNS FROM berkas_siswa LIKE 'jenis_berkas'");
$jenis_row = $jenis_result->fetch_assoc();
$enum_string = $jenis_row['Type'];
preg_match_all("/'([^']+)'/", $enum_string, $matches);
$jenis_options = $matches[1];

// Get statistics
$stats = $conn->query("
    SELECT
        COUNT(*) as total_berkas,
        COUNT(DISTINCT siswa_nisn) as siswa_dengan_berkas,
        SUM(ukuran_file) as total_ukuran
    FROM berkas_siswa
")->fetch_assoc();

function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Berkas Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .berkas-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        .berkas-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .file-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .file-icon.pdf { color: #dc3545; }
        .file-icon.image { color: #007bff; }
        .file-icon.word { color: #0d6efd; }
        .file-icon.default { color: #6c757d; }

        .stats-card {
            border-left: 4px solid;
        }
        .stats-card.total { border-left-color: #007bff; }
        .stats-card.siswa { border-left-color: #28a745; }
        .stats-card.ukuran { border-left-color: #ffc107; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link active" href="index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-folder text-primary me-2"></i>Berkas Siswa</h2>
            <a href="upload.php" class="btn btn-success">
                <i class="fas fa-upload me-1"></i>Upload Berkas
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>Berkas berhasil diupload!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-trash me-2"></i>Berkas berhasil dihapus!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card total">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Total Berkas</h5>
                                <h2 class="text-primary"><?= $stats['total_berkas'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card siswa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Siswa dengan Berkas</h5>
                                <h2 class="text-success"><?= $stats['siswa_dengan_berkas'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card ukuran">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Total Ukuran</h5>
                                <h2 class="text-warning"><?= formatFileSize($stats['total_ukuran']) ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-hdd fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>