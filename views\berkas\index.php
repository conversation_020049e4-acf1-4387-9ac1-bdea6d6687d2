<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login
requireLogin(true);

require_once '../../config/db.php';
require_once '../../config/functions.php';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$filter_jenis = $_GET['filter_jenis'] ?? '';
$filter_siswa = $_GET['filter_siswa'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(b.nama_file LIKE ? OR s.nama_lengkap LIKE ? OR b.keterangan LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $types .= 'sss';
}

if (!empty($filter_jenis)) {
    $where_conditions[] = "b.jenis_berkas = ?";
    $params[] = $filter_jenis;
    $types .= 's';
}

if (!empty($filter_siswa)) {
    $where_conditions[] = "b.siswa_nisn = ?";
    $params[] = $filter_siswa;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total records
$count_sql = "
    SELECT COUNT(*) as total
    FROM berkas_siswa b
    JOIN siswa s ON b.siswa_nisn = s.nisn
    $where_clause
";

if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
} else {
    $total_records = $conn->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $limit);

// Get berkas data
$sql = "
    SELECT b.*, s.nama_lengkap, s.kelas_id, k.nama_kelas, u.username as uploaded_by_name
    FROM berkas_siswa b
    JOIN siswa s ON b.siswa_nisn = s.nisn
    LEFT JOIN kelas k ON s.kelas_id = k.id
    JOIN users u ON b.uploaded_by = u.id
    $where_clause
    ORDER BY b.uploaded_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;
$types .= 'ii';

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$berkas_list = $stmt->get_result();

// Get filter options
$siswa_options = $conn->query("SELECT nisn, nama_lengkap FROM siswa WHERE status = 'Aktif' ORDER BY nama_lengkap");
$jenis_result = $conn->query("SHOW COLUMNS FROM berkas_siswa LIKE 'jenis_berkas'");
$jenis_row = $jenis_result->fetch_assoc();
$enum_string = $jenis_row['Type'];
preg_match_all("/'([^']+)'/", $enum_string, $matches);
$jenis_options = $matches[1];

// Get statistics
$stats = $conn->query("
    SELECT
        COUNT(*) as total_berkas,
        COUNT(DISTINCT siswa_nisn) as siswa_dengan_berkas,
        SUM(ukuran_file) as total_ukuran
    FROM berkas_siswa
")->fetch_assoc();

function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Berkas Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .berkas-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        .berkas-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .file-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .file-icon.pdf { color: #dc3545; }
        .file-icon.image { color: #007bff; }
        .file-icon.word { color: #0d6efd; }
        .file-icon.default { color: #6c757d; }

        .stats-card {
            border-left: 4px solid;
        }
        .stats-card.total { border-left-color: #007bff; }
        .stats-card.siswa { border-left-color: #28a745; }
        .stats-card.ukuran { border-left-color: #ffc107; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link active" href="index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Tab Navigation -->
        <div class="mb-4">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-user me-2"></i>Informasi Personal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-calendar-check me-2"></i>Data Absensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" style="color: #6c757d;">
                        <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="#" style="background-color: #007bff; color: white;">
                        <i class="fas fa-folder me-2"></i>Berkas Siswa
                    </a>
                </li>
            </ul>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-file-alt" style="color: #667eea; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0"><?php echo $stats['total_berkas']; ?></h3>
                                <small>Total Berkas</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-layer-group" style="color: #f093fb; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">1</h3>
                                <small>Kategori</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-clock" style="color: #4facfe; font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">05/07/2025</h3>
                                <small>Terakhir Upload</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-folder text-primary me-2"></i>Daftar Berkas</h4>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                <i class="fas fa-upload me-1"></i>Upload Berkas
            </button>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>Berkas berhasil diupload!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-trash me-2"></i>Berkas berhasil dihapus!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card total">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Total Berkas</h5>
                                <h2 class="text-primary"><?= $stats['total_berkas'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-file fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card siswa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Siswa dengan Berkas</h5>
                                <h2 class="text-success"><?= $stats['siswa_dengan_berkas'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card ukuran">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Total Ukuran</h5>
                                <h2 class="text-warning"><?= formatFileSize($stats['total_ukuran']) ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-hdd fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Berkas List -->
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Berkas</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($berkas_list->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Dokumen Identitas</th>
                                            <th>Kartu Keluarga</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                                    <span>1 berkas</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-image text-warning me-2"></i>
                                                    <span>-</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm" title="Lihat">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" title="Download">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" title="Hapus">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Belum ada berkas</h5>
                                <p class="text-muted">Klik tombol "Upload Berkas" untuk menambah berkas baru</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Format yang didukung:</small>
                            <div class="mt-1">
                                <span class="badge bg-light text-dark me-1">PDF</span>
                                <span class="badge bg-light text-dark me-1">JPG</span>
                                <span class="badge bg-light text-dark me-1">PNG</span>
                                <span class="badge bg-light text-dark me-1">DOC</span>
                                <span class="badge bg-light text-dark">DOCX</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Ukuran maksimal: <strong>5MB</strong></small>
                        </div>
                        <hr>
                        <div class="text-center">
                            <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                            <p class="small text-muted mb-0">File Anda aman dan terenkripsi</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div class="modal fade" id="uploadModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Upload Berkas Siswa</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" action="../../controllers/BerkasController.php" enctype="multipart/form-data" id="formUploadBerkas">
                        <div class="modal-body">
                            <!-- Pilih Siswa -->
                            <div class="mb-3">
                                <label class="form-label">Pilih Siswa <span class="text-danger">*</span></label>
                                <select class="form-select" name="siswa_nisn" required>
                                    <option value="">-- Pilih Siswa --</option>
                                    <?php
                                    $siswa_result->data_seek(0); // Reset pointer
                                    while ($siswa = $siswa_result->fetch_assoc()):
                                    ?>
                                        <option value="<?= htmlspecialchars($siswa['nisn']) ?>">
                                            <?= htmlspecialchars($siswa['nama_lengkap']) ?> - <?= htmlspecialchars($siswa['nisn']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <!-- Jenis Berkas -->
                            <div class="mb-3">
                                <label class="form-label">Jenis Berkas <span class="text-danger">*</span></label>
                                <select class="form-select" name="jenis_berkas" required>
                                    <option value="">Pilih Jenis Berkas</option>
                                    <?php foreach ($jenis_options as $jenis): ?>
                                        <option value="<?= htmlspecialchars($jenis) ?>"><?= htmlspecialchars($jenis) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- File Berkas -->
                            <div class="mb-3">
                                <label class="form-label">File Berkas <span class="text-danger">*</span></label>
                                <div class="file-drop-area border-2 border-dashed border-success rounded p-4 text-center" id="fileDropArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                    <h6>Drag & Drop file di sini</h6>
                                    <p class="text-muted mb-3">atau klik untuk memilih file</p>
                                    <input type="file" name="file_berkas" id="fileInput" class="d-none" required
                                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <button type="button" class="btn btn-outline-success" onclick="document.getElementById('fileInput').click()">
                                        <i class="fas fa-folder-open me-1"></i>Choose File
                                    </button>
                                    <div class="form-text mt-2">
                                        Format: PDF, JPG, PNG, DOC, DOCX. Maksimal 5MB
                                    </div>
                                </div>
                                <div id="filePreview" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-file me-2"></i>
                                        <span id="fileName"></span>
                                        <span class="badge bg-primary ms-2" id="fileSize"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Keterangan -->
                            <div class="mb-3">
                                <label class="form-label">Keterangan (Opsional)</label>
                                <textarea class="form-control" name="keterangan" rows="3"
                                          placeholder="Tambahkan keterangan untuk berkas ini..."></textarea>
                            </div>

                            <!-- Preview File akan muncul di sini -->
                            <div id="previewFile" class="mt-3" style="display: none;">
                                <h6>Preview file akan muncul di sini</h6>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" name="upload_berkas" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>Upload Berkas
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const fileDropArea = document.getElementById('fileDropArea');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        // Drag and drop functionality
        fileDropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileDropArea.classList.add('border-primary');
        });

        fileDropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileDropArea.classList.remove('border-primary');
        });

        fileDropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileDropArea.classList.remove('border-primary');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFilePreview(files[0]);
            }
        });

        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                showFilePreview(this.files[0]);
            }
        });

        function showFilePreview(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            filePreview.style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form validation
        document.getElementById('formUploadBerkas').addEventListener('submit', function(e) {
            const jenis = document.querySelector('[name="jenis_berkas"]').value;
            const file = document.querySelector('[name="file_berkas"]').files[0];

            if (!jenis || !file) {
                e.preventDefault();
                alert('Mohon lengkapi semua field yang wajib diisi!');
                return false;
            }

            // Check file size (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                e.preventDefault();
                alert('Ukuran file terlalu besar! Maksimal 5MB.');
                return false;
            }
        });
    </script>
</body>
</html>