<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Kelas - SISWA APP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn-group .btn {
            margin-right: 2px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Data Siswa</a>
                <a class="nav-link active" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-school me-2"></i>Data Kelas</h4>
                <small class="text-muted">Manajemen kelas sekolah</small>
            </div>
            <div>
                <a href="#" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Kelas
                </a>
            </div>
        </div>

        <!-- Alert Messages -->
        <div class="alert alert-success alert-dismissible fade show" id="successAlert" style="display: none;">
            <i class="fas fa-check-circle me-2"></i><span id="successMessage"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th>ID</th>
                            <th>Nama Kelas</th>
                            <th>Tingkat</th>
                            <th>Kurikulum</th>
                            <th>Tahun Pelajaran</th>
                            <th>Wali Kelas</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><strong>Kelas Persiapan Pertama</strong></td>
                            <td><span class="badge bg-warning">KPP</span></td>
                            <td><span class="badge bg-secondary">Kurikulum Seminari</span></td>
                            <td>2024/2025</td>
                            <td>Rm. Antonius Widodo, Pr.</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Detail" onclick="showDetail('Kelas Persiapan Pertama')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit" onclick="showEdit('1', 'Kelas Persiapan Pertama')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus" onclick="confirmDeleteKelas('1', 'Kelas Persiapan Pertama')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td><strong>Kelas X-1</strong></td>
                            <td><span class="badge bg-primary">X-1</span></td>
                            <td><span class="badge bg-info">Kurikulum Merdeka</span></td>
                            <td>2024/2025</td>
                            <td>Y. Guntur Cahyo Dewantoro, ST.</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Detail" onclick="showDetail('Kelas X-1')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit" onclick="showEdit('2', 'Kelas X-1')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus" onclick="confirmDeleteKelas('2', 'Kelas X-1')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td><strong>Kelas XI-2</strong></td>
                            <td><span class="badge bg-success">XI-2</span></td>
                            <td><span class="badge bg-danger">Kurikulum Deep Learning</span></td>
                            <td>2024/2025</td>
                            <td>Dr. Bambang Sutrisno, M.Kom.</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Detail" onclick="showDetail('Kelas XI-2')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit" onclick="showEdit('3', 'Kelas XI-2')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus" onclick="confirmDeleteKelas('3', 'Kelas XI-2')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td><strong>Kelas Persiapan Atas</strong></td>
                            <td><span class="badge bg-dark">KPA</span></td>
                            <td><span class="badge bg-secondary">Kurikulum Seminari</span></td>
                            <td>2024/2025</td>
                            <td>Rm. Yohanes Baptista, Pr.</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Detail" onclick="showDetail('Kelas Persiapan Atas')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit" onclick="showEdit('4', 'Kelas Persiapan Atas')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus" onclick="confirmDeleteKelas('4', 'Kelas Persiapan Atas')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showDetail(namaKelas) {
            Swal.fire({
                title: 'Detail Kelas',
                html: `
                    <div class="text-start">
                        <h5>${namaKelas}</h5>
                        <hr>
                        <p><strong>Tingkat:</strong> KPA (Kelas Percepatan Akses)</p>
                        <p><strong>Kurikulum:</strong> Kurikulum Merdeka</p>
                        <p><strong>Tahun Pelajaran:</strong> 2024/2025</p>
                        <p><strong>Wali Kelas:</strong> Y. Guntur Cahyo Dewantoro, ST.</p>
                        <p><strong>Kapasitas:</strong> 30 siswa</p>
                        <p><strong>Jumlah Siswa:</strong> 25 siswa</p>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'Tutup',
                width: '500px'
            });
        }

        function showEdit(id, namaKelas) {
            Swal.fire({
                title: 'Edit Kelas',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Nama Kelas</label>
                            <input type="text" class="form-control" value="${namaKelas}" id="editNamaKelas">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tingkat</label>
                            <select class="form-select" id="editTingkat">
                                <option value="KPP">KPP (Kelas Persiapan Pertama)</option>
                                <option value="X-1">X-1</option>
                                <option value="X-2">X-2</option>
                                <option value="XI-1">XI-1</option>
                                <option value="XI-2">XI-2</option>
                                <option value="XII-1">XII-1</option>
                                <option value="XII-2">XII-2</option>
                                <option value="KPA">KPA (Kelas Persiapan Atas)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Kurikulum</label>
                            <select class="form-select" id="editKurikulum">
                                <option value="Kurikulum Seminari">Kurikulum Seminari</option>
                                <option value="Kurikulum Merdeka">Kurikulum Merdeka</option>
                                <option value="Kurikulum Deep Learning">Kurikulum Deep Learning</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tahun Pelajaran</label>
                            <input type="text" class="form-control" value="2024/2025" id="editTahunPelajaran">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Wali Kelas</label>
                            <input type="text" class="form-control" value="Y. Guntur Cahyo Dewantoro, ST." id="editWaliKelas">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Kapasitas</label>
                            <input type="number" class="form-control" value="30" id="editKapasitas" min="1" max="50">
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan Perubahan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '600px',
                preConfirm: () => {
                    const namaKelas = document.getElementById('editNamaKelas').value;
                    const tingkat = document.getElementById('editTingkat').value;
                    const kurikulum = document.getElementById('editKurikulum').value;
                    const tahunPelajaran = document.getElementById('editTahunPelajaran').value;
                    const waliKelas = document.getElementById('editWaliKelas').value;
                    const kapasitas = document.getElementById('editKapasitas').value;
                    
                    if (!namaKelas || !tahunPelajaran) {
                        Swal.showValidationMessage('Nama kelas dan tahun pelajaran tidak boleh kosong');
                        return false;
                    }
                    
                    return { namaKelas, tingkat, kurikulum, tahunPelajaran, waliKelas, kapasitas };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Simulate update success
                    showSuccessMessage('Data kelas berhasil diperbarui!');
                }
            });
        }

        function confirmDeleteKelas(id, namaKelas) {
            Swal.fire({
                title: 'Konfirmasi Hapus Kelas',
                html: `Apakah Anda yakin ingin menghapus kelas:<br><strong>${namaKelas}</strong>?<br><br><small class="text-warning">⚠️ Pastikan tidak ada siswa di kelas ini!</small>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash me-1"></i>Ya, Hapus Kelas!',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Menghapus Kelas...',
                        text: 'Mohon tunggu sebentar',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    // Simulate delete process
                    setTimeout(() => {
                        Swal.close();
                        showSuccessMessage('Kelas berhasil dihapus!');
                        
                        // Remove row from table (demo purpose)
                        const rows = document.querySelectorAll('tbody tr');
                        rows.forEach(row => {
                            if (row.textContent.includes(namaKelas)) {
                                row.remove();
                            }
                        });
                    }, 2000);
                }
            });
        }

        function showSuccessMessage(message) {
            const alert = document.getElementById('successAlert');
            const messageSpan = document.getElementById('successMessage');
            messageSpan.textContent = message;
            alert.style.display = 'block';
            alert.classList.add('show');
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 150);
            }, 5000);
        }
    </script>
</body>
</html>
