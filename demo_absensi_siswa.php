<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Absensi - <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .tab-nav {
            background: white;
            border-radius: 10px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-nav .nav-link {
            border-radius: 8px;
            color: #6c757d;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            margin: 0 0.25rem;
            transition: all 0.3s;
        }
        
        .tab-nav .nav-link.active {
            background: #007bff;
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            margin-bottom: 1rem;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        
        .stats-kehadiran { background: linear-gradient(135deg, #4CAF50, #45a049); }
        .stats-sakit { background: linear-gradient(135deg, #f44336, #d32f2f); }
        .stats-izin { background: linear-gradient(135deg, #ff9800, #f57c00); }
        .stats-alpa { background: linear-gradient(135deg, #9e9e9e, #757575); }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .riwayat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Data Siswa</a>
                <a class="nav-link" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Data Absensi</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="profile-avatar">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <h3 class="mb-1">Ahmad Rizki Pratama</h3>
                        <p class="mb-1">NISN: 0076116641</p>
                        <p class="mb-0">Kelas: X-1</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-nav">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Kembali ke halaman detail siswa')">
                        <i class="fas fa-user me-2"></i>Informasi Personal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="#">
                        <i class="fas fa-calendar-check me-2"></i>Data Absensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-folder me-2"></i>Berkas Siswa
                    </a>
                </li>
            </ul>
        </div>

        <!-- Statistik Absensi -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-kehadiran text-white">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number">100.0%</div>
                    <div class="stats-label">Kehadiran</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-sakit text-white">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <div class="stats-number">0</div>
                    <div class="stats-label">Sakit</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-izin text-white">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <div class="stats-number">0</div>
                    <div class="stats-label">Ijin</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-alpa text-white">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stats-number">0</div>
                    <div class="stats-label">Alpha</div>
                </div>
            </div>
        </div>

        <!-- Riwayat Absensi -->
        <div class="riwayat-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Absensi</h5>
                <button class="btn btn-primary btn-sm" onclick="tambahAbsensi()">
                    <i class="fas fa-plus me-1"></i>Tambah Absensi
                </button>
            </div>

            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h5>Belum ada catatan absensi</h5>
                <p>Siswa ini belum memiliki catatan ketidakhadiran</p>
                <button class="btn btn-primary" onclick="tambahAbsensi()">
                    <i class="fas fa-plus me-1"></i>Tambah Absensi Pertama
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function tambahAbsensi() {
            Swal.fire({
                title: 'Tambah Absensi',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Siswa</label>
                            <input type="text" class="form-control" value="Ahmad Rizki Pratama (0076116641)" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="tanggalAbsensi" value="2025-07-05">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status Kehadiran</label>
                            <select class="form-select" id="statusAbsensi">
                                <option value="Hadir">Hadir</option>
                                <option value="Sakit">Sakit</option>
                                <option value="Izin">Izin</option>
                                <option value="Alpa">Alpa</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Jam Masuk</label>
                                <input type="time" class="form-control" id="jamMasuk" value="07:15">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Jam Keluar</label>
                                <input type="time" class="form-control" id="jamKeluar" value="15:30">
                            </div>
                        </div>
                        <div class="mb-3 mt-3">
                            <label class="form-label">Catatan</label>
                            <textarea class="form-control" id="catatanAbsensi" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '500px'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data absensi berhasil ditambahkan',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        // Simulate page reload with new data
                        location.reload();
                    });
                }
            });
        }
    </script>
</body>
</html>
