<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit;
}

require_once '../../config/db.php';
require_once '../../models/Catatan.php';

$catatan_model = new Catatan($conn);

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$search = $_GET['search'] ?? '';
$filter_jenis = $_GET['filter_jenis'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';

$total_records = $catatan_model->getTotalCount($search, $filter_jenis, $filter_status);
$total_pages = ceil($total_records / $limit);

$catatan_list = $catatan_model->getAllCatatan($page, $limit, $search, $filter_jenis, $filter_status);
$jenis_options = $catatan_model->getJenisCatatanOptions();
$statistik = $catatan_model->getStatistikCatatan();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Catatan Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .priority-badge {
            font-size: 0.75rem;
        }
        .priority-rendah { background-color: #28a745; }
        .priority-sedang { background-color: #ffc107; color: #000; }
        .priority-tinggi { background-color: #fd7e14; }
        .priority-sangat-tinggi { background-color: #dc3545; }
        
        .status-badge {
            font-size: 0.75rem;
        }
        .status-aktif { background-color: #17a2b8; }
        .status-selesai { background-color: #28a745; }
        .status-ditunda { background-color: #ffc107; color: #000; }
        .status-dibatalkan { background-color: #6c757d; }
        
        .card-stats {
            border-left: 4px solid;
        }
        .card-stats.total { border-left-color: #007bff; }
        .card-stats.aktif { border-left-color: #17a2b8; }
        .card-stats.selesai { border-left-color: #28a745; }
        .card-stats.prioritas { border-left-color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link active" href="index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-sticky-note text-primary me-2"></i>Catatan Siswa</h2>
            <a href="tambah.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Tambah Catatan
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>Catatan berhasil ditambahkan!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['updated'])): ?>
            <div class="alert alert-info alert-dismissible fade show">
                <i class="fas fa-edit me-2"></i>Catatan berhasil diupdate!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-trash me-2"></i>Catatan berhasil dihapus!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistik Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stats total">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Total Catatan</h5>
                                <h2 class="text-primary"><?= $statistik['total_catatan'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clipboard-list fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats aktif">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Aktif</h5>
                                <h2 class="text-info"><?= $statistik['aktif'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats selesai">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Selesai</h5>
                                <h2 class="text-success"><?= $statistik['selesai'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats prioritas">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Prioritas Tinggi</h5>
                                <h2 class="text-danger"><?= $statistik['prioritas_tinggi'] + $statistik['prioritas_sangat_tinggi'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter dan Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Cari Catatan</label>
                        <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Cari judul, isi, atau nama siswa...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Jenis Catatan</label>
                        <select class="form-select" name="filter_jenis">
                            <option value="">Semua Jenis</option>
                            <?php foreach ($jenis_options as $jenis): ?>
                                <option value="<?= $jenis ?>" <?= $filter_jenis == $jenis ? 'selected' : '' ?>><?= $jenis ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="filter_status">
                            <option value="">Semua Status</option>
                            <option value="Aktif" <?= $filter_status == 'Aktif' ? 'selected' : '' ?>>Aktif</option>
                            <option value="Selesai" <?= $filter_status == 'Selesai' ? 'selected' : '' ?>>Selesai</option>
                            <option value="Ditunda" <?= $filter_status == 'Ditunda' ? 'selected' : '' ?>>Ditunda</option>
                            <option value="Dibatalkan" <?= $filter_status == 'Dibatalkan' ? 'selected' : '' ?>>Dibatalkan</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabel Catatan -->
        <div class="card">
            <div class="card-body">
                <?php if ($catatan_list->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Siswa</th>
                                <th>Jenis Catatan</th>
                                <th>Judul</th>
                                <th>Prioritas</th>
                                <th>Status</th>
                                <th>Tanggal</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = ($page - 1) * $limit + 1;
                            while ($row = $catatan_list->fetch_assoc()):
                            ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td>
                                    <strong><?= htmlspecialchars($row['nama_lengkap']) ?></strong><br>
                                    <small class="text-muted"><?= $row['nama_kelas'] ?? 'Tidak ada kelas' ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($row['jenis_catatan']) ?></span>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($row['judul_catatan']) ?></strong><br>
                                    <small class="text-muted"><?= substr(htmlspecialchars($row['isi_catatan']), 0, 50) ?>...</small>
                                </td>
                                <td>
                                    <span class="badge priority-badge priority-<?= strtolower(str_replace(' ', '-', $row['tingkat_prioritas'])) ?>">
                                        <?= $row['tingkat_prioritas'] ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge status-badge status-<?= strtolower($row['status']) ?>">
                                        <?= $row['status'] ?>
                                    </span>
                                </td>
                                <td>
                                    <?= date('d/m/Y', strtotime($row['created_at'])) ?><br>
                                    <small class="text-muted">oleh <?= htmlspecialchars($row['created_by_name']) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="detail.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" title="Detail">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?= $row['id'] ?>" class="btn btn-warning btn-sm" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="../../controllers/CatatanController.php?hapus=<?= $row['id'] ?>"
                                           class="btn btn-danger btn-sm" title="Hapus"
                                           onclick="return confirm('Yakin ingin menghapus catatan ini?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada catatan siswa</h5>
                    <p class="text-muted">Klik tombol "Tambah Catatan" untuk menambahkan catatan pertama.</p>
                    <a href="tambah.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Tambah Catatan Pertama
                    </a>
                </div>
                <?php endif; ?>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page-1 ?>&search=<?= urlencode($search) ?>&filter_jenis=<?= urlencode($filter_jenis) ?>&filter_status=<?= urlencode($filter_status) ?>">Previous</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&filter_jenis=<?= urlencode($filter_jenis) ?>&filter_status=<?= urlencode($filter_status) ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page+1 ?>&search=<?= urlencode($search) ?>&filter_jenis=<?= urlencode($filter_jenis) ?>&filter_status=<?= urlencode($filter_status) ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
