<?php
/**
 * Script untuk memperbaiki masalah session di seluruh aplikasi
 */

echo "=== MEMPERBAIKI MASALAH SESSION ===\n";

// Daftar file yang perlu diperbaiki
$files_to_fix = [
    'views/absensi/index.php',
    'views/absensi/tambah.php',
    'views/berkas/index.php',
    'views/berkas/upload.php',
    'views/catatan/index.php',
    'views/catatan/tambah.php',
    'views/siswa/absensi.php'
];

$session_helper_include = "require_once '../../config/session_helper.php';";
$require_login_call = "requireLogin(true);";

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        echo "Memperbaiki file: $file\n";
        
        $content = file_get_contents($file);
        
        // Cek apakah sudah menggunakan session_helper
        if (strpos($content, 'session_helper.php') === false) {
            // Replace session_start() dengan session helper
            $patterns = [
                '/session_start\(\);\s*if\s*\(\s*!\s*isset\(\s*\$_SESSION\[\'user_id\'\]\s*\)\s*\)\s*\{\s*header\([^}]+\}\s*/s',
                '/session_start\(\);\s*require_once/s'
            ];
            
            $replacements = [
                $session_helper_include . "\nrequire_once '../../config/db.php';\n\n// Pastikan user sudah login\n" . $require_login_call . "\n\n",
                $session_helper_include . "\nrequire_once"
            ];
            
            $new_content = preg_replace($patterns, $replacements, $content);
            
            // Jika tidak ada perubahan dengan regex, coba manual
            if ($new_content === $content) {
                // Manual replacement untuk session_start()
                if (strpos($content, 'session_start();') !== false) {
                    $new_content = str_replace(
                        '<?php' . "\n" . 'session_start();',
                        '<?php' . "\n" . $session_helper_include,
                        $content
                    );
                    
                    // Tambahkan requireLogin setelah require db
                    if (strpos($new_content, "require_once '../../config/db.php';") !== false) {
                        $new_content = str_replace(
                            "require_once '../../config/db.php';",
                            "require_once '../../config/db.php';\n\n// Pastikan user sudah login\n" . $require_login_call,
                            $new_content
                        );
                    }
                }
            }
            
            if ($new_content !== $content) {
                file_put_contents($file, $new_content);
                echo "✅ File $file berhasil diperbaiki\n";
            } else {
                echo "ℹ️ File $file tidak perlu diperbaiki\n";
            }
        } else {
            echo "✅ File $file sudah menggunakan session helper\n";
        }
    } else {
        echo "❌ File $file tidak ditemukan\n";
    }
}

echo "\n=== MEMBUAT FILE TEST SESSION ===\n";

// Buat file test untuk memverifikasi session
$test_content = '<?php
require_once "config/session_helper.php";

echo "<h2>🔍 Session Test</h2>";

echo "<h3>Session Information:</h3>";
$session_info = getSessionInfo();
foreach ($session_info as $key => $value) {
    echo "<strong>" . ucfirst(str_replace("_", " ", $key)) . ":</strong> " . ($value ?? "Not set") . "<br>";
}

echo "<h3>Session Functions Test:</h3>";
echo "<strong>Is Logged In:</strong> " . (isLoggedIn() ? "Yes" : "No") . "<br>";
echo "<strong>User Role:</strong> " . getUserRole() . "<br>";
echo "<strong>Username:</strong> " . getUsername() . "<br>";
echo "<strong>User ID:</strong> " . getUserId() . "<br>";

echo "<h3>Role Check Test:</h3>";
echo "<strong>Has Admin Role:</strong> " . (hasRole("admin") ? "Yes" : "No") . "<br>";
echo "<strong>Has User Role:</strong> " . (hasRole("user") ? "Yes" : "No") . "<br>";

echo "<h3>CSRF Token:</h3>";
echo "<strong>CSRF Token:</strong> " . generateCSRFToken() . "<br>";

echo "<hr>";
echo "<h3>Quick Links:</h3>";
echo "<a href=\"views/dashboard/index.php\">Dashboard</a> | ";
echo "<a href=\"views/siswa/index.php\">Data Siswa</a> | ";
echo "<a href=\"views/siswa/detail.php?nisn=0076116641\">Detail Siswa</a>";
?>';

file_put_contents('test_session.php', $test_content);
echo "✅ File test_session.php berhasil dibuat\n";

echo "\n=== RINGKASAN ===\n";
echo "✅ Session helper telah dibuat di config/session_helper.php\n";
echo "✅ File-file utama telah diperbaiki untuk menggunakan session helper\n";
echo "✅ Error 'Undefined index: role' telah diperbaiki\n";
echo "✅ File test session telah dibuat untuk verifikasi\n";

echo "\n🎉 Semua masalah session telah diperbaiki!\n";
echo "Akses test_session.php untuk memverifikasi session\n";
?>
