<?php
require_once 'config/db.php';
require_once 'config/functions.php';

echo "<h2>🔍 User Database Check</h2>";

// Check if users table exists and has data
$result = $conn->query("SELECT * FROM users");
if ($result->num_rows > 0) {
    echo "<h3>✅ Users found in database:</h3>";
    while ($row = $result->fetch_assoc()) {
        echo "<strong>ID:</strong> " . $row['id'] . "<br>";
        echo "<strong>Username:</strong> " . $row['username'] . "<br>";
        echo "<strong>Password Hash:</strong> " . substr($row['password'], 0, 20) . "...<br>";
        echo "<strong>Role:</strong> " . $row['role'] . "<br>";
        echo "<hr>";
    }
} else {
    echo "<h3>❌ No users found in database!</h3>";
    echo "<p>Run create_sample_data.php to create sample users.</p>";
}

// Test password verification
echo "<h3>🔐 Password Verification Test</h3>";
$test_password = 'admin123';
$result = $conn->query("SELECT * FROM users WHERE username = 'admin'");
if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<strong>Testing password 'admin123' for user 'admin':</strong><br>";
    
    // Test with verifyPassword function
    if (function_exists('verifyPassword')) {
        $verify_result = verifyPassword($test_password, $user['password']);
        echo "verifyPassword() result: " . ($verify_result ? "✅ MATCH" : "❌ NO MATCH") . "<br>";
    } else {
        echo "❌ verifyPassword() function not found!<br>";
    }
    
    // Test with password_verify
    $verify_result2 = password_verify($test_password, $user['password']);
    echo "password_verify() result: " . ($verify_result2 ? "✅ MATCH" : "❌ NO MATCH") . "<br>";
    
    // Test direct comparison (if password is not hashed)
    $direct_match = ($test_password === $user['password']);
    echo "Direct comparison result: " . ($direct_match ? "✅ MATCH" : "❌ NO MATCH") . "<br>";
    
} else {
    echo "❌ User 'admin' not found!<br>";
}

echo "<hr>";
echo "<p><a href='views/auth/login.php'>← Back to Login</a></p>";
?>
