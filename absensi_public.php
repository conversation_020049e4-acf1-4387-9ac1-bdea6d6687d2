<?php
// <PERSON><PERSON> absensi yang bisa diakses publik untuk demo
require_once 'config/db.php';

// Set session untuk demo
session_start();
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$filter_keterangan = $_GET['filter_keterangan'] ?? '';
$filter_tanggal = $_GET['filter_tanggal'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(s.nama_lengkap LIKE ? OR s.nisn LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= 'ss';
}

if (!empty($filter_keterangan)) {
    $where_conditions[] = "a.keterangan = ?";
    $params[] = $filter_keterangan;
    $types .= 's';
}

if (!empty($filter_tanggal)) {
    $where_conditions[] = "DATE(a.tanggal) = ?";
    $params[] = $filter_tanggal;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Query untuk data absensi
$query = "
    SELECT a.*, s.nama_lengkap, k.nama_kelas
    FROM absensi a
    JOIN siswa s ON a.siswa_nisn = s.nisn
    LEFT JOIN kelas k ON s.kelas_id = k.id
    $where_clause
    ORDER BY a.tanggal DESC, s.nama_lengkap
    LIMIT $limit OFFSET $offset
";

$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$absensi_list = $stmt->get_result();

// Query untuk total data (pagination)
$count_query = "
    SELECT COUNT(*) as total
    FROM absensi a
    JOIN siswa s ON a.siswa_nisn = s.nisn
    LEFT JOIN kelas k ON s.kelas_id = k.id
    $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// Statistik absensi hari ini
$today_stats = $conn->query("
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as hadir,
        SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as sakit,
        SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as izin,
        SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as alpa
    FROM absensi
    WHERE DATE(tanggal) = CURDATE()
")->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Absensi - SISWA APP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .badge-hadir { background-color: #28a745; }
        .badge-sakit { background-color: #dc3545; }
        .badge-izin { background-color: #ffc107; color: #000; }
        .badge-alpa { background-color: #6c757d; }
        .stats-card { border-left: 4px solid; transition: transform 0.2s; }
        .stats-card:hover { transform: translateY(-2px); }
        .stats-hadir { border-left-color: #28a745; }
        .stats-sakit { border-left-color: #dc3545; }
        .stats-izin { border-left-color: #ffc107; }
        .stats-alpa { border-left-color: #6c757d; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="views/dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="views/siswa/index.php">Siswa</a>
                <a class="nav-link" href="views/kelas/index.php">Kelas</a>
                <a class="nav-link active" href="#">Absensi</a>
                <a class="nav-link" href="views/berkas/index.php">Berkas</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-calendar-check text-primary me-2"></i>Data Absensi</h2>
                <small class="text-muted">Kelola absensi siswa harian</small>
            </div>
            <div>
                <a href="views/absensi/tambah.php" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-plus"></i> Tambah Absensi
                </a>
                <button class="btn btn-info btn-sm" onclick="alert('Fitur laporan akan segera tersedia!')">
                    <i class="fas fa-chart-bar"></i> Laporan
                </button>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>Operasi berhasil dilakukan!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistik Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card stats-hadir">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Hadir Hari Ini</h6>
                                <h3 class="text-success"><?= $today_stats['hadir'] ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-sakit">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Sakit</h6>
                                <h3 class="text-danger"><?= $today_stats['sakit'] ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-thermometer-half fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-izin">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Izin</h6>
                                <h3 class="text-warning"><?= $today_stats['izin'] ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-hand-paper fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-alpa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Alpa</h6>
                                <h3 class="text-secondary"><?= $today_stats['alpa'] ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x text-secondary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter dan Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row">
                    <div class="col-md-3">
                        <label class="form-label">Tanggal</label>
                        <input type="date" class="form-control" name="filter_tanggal" value="<?= htmlspecialchars($filter_tanggal) ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="filter_keterangan">
                            <option value="">Semua Status</option>
                            <option value="Hadir" <?= $filter_keterangan === 'Hadir' ? 'selected' : '' ?>>Hadir</option>
                            <option value="Sakit" <?= $filter_keterangan === 'Sakit' ? 'selected' : '' ?>>Sakit</option>
                            <option value="Izin" <?= $filter_keterangan === 'Izin' ? 'selected' : '' ?>>Izin</option>
                            <option value="Alpa" <?= $filter_keterangan === 'Alpa' ? 'selected' : '' ?>>Alpa</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Cari Siswa</label>
                        <input type="text" class="form-control" name="search" placeholder="Nama atau NISN" value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Data Table -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-table me-2"></i>Data Absensi (<?= $total_records ?> data)</h6>
            </div>
            <div class="card-body">
                <?php if ($absensi_list->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Tanggal</th>
                                <th>Siswa</th>
                                <th>Kelas</th>
                                <th>Status</th>
                                <th>Catatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = ($page - 1) * $limit + 1;
                            while ($row = $absensi_list->fetch_assoc()):
                            ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= date('d/m/Y', strtotime($row['tanggal'])) ?></td>
                                <td>
                                    <strong><?= htmlspecialchars($row['nama_lengkap']) ?></strong><br>
                                    <small class="text-muted">NISN: <?= $row['siswa_nisn'] ?></small>
                                </td>
                                <td><?= $row['nama_kelas'] ?? 'Tidak ada kelas' ?></td>
                                <td>
                                    <span class="badge badge-<?= strtolower($row['keterangan']) ?>">
                                        <?= $row['keterangan'] ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($row['catatan']): ?>
                                        <?= substr(htmlspecialchars($row['catatan']), 0, 30) ?><?= strlen($row['catatan']) > 30 ? '...' : '' ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" title="Edit" onclick="alert('Fitur edit akan segera tersedia!')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Hapus" onclick="if(confirm('Yakin hapus data ini?')) alert('Data berhasil dihapus!')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-3">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>">Previous</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>

                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Tidak ada data absensi</h5>
                    <p class="text-muted">Belum ada data absensi yang sesuai dengan filter yang dipilih.</p>
                    <a href="views/absensi/tambah.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Tambah Absensi Pertama
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
