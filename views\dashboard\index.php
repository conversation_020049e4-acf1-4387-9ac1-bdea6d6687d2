<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login (untuk demo, auto-set session)
requireLogin(true);

// Get statistik dashboard
$stats = [];

// Total siswa
$result = $conn->query("SELECT COUNT(*) as total FROM siswa WHERE status = 'Aktif'");
$stats['total_siswa'] = $result->fetch_assoc()['total'];

// Total kelas
$result = $conn->query("SELECT COUNT(*) as total FROM kelas");
$stats['total_kelas'] = $result->fetch_assoc()['total'];

// Absensi hari ini
$result = $conn->query("SELECT COUNT(*) as total FROM absensi WHERE DATE(tanggal) = CURDATE()");
$stats['absensi_hari_ini'] = $result->fetch_assoc()['total'];

// Total catatan
$result = $conn->query("SELECT COUNT(*) as total FROM catatan_siswa");
$stats['total_catatan'] = $result->fetch_assoc()['total'];

// Statistik absensi bulan ini
$result = $conn->query("
    SELECT
        SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as hadir,
        SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as sakit,
        SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as izin,
        SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as alpa
    FROM absensi
    WHERE MONTH(tanggal) = MONTH(CURRENT_DATE())
    AND YEAR(tanggal) = YEAR(CURRENT_DATE())
");
$absensi_stats = $result->fetch_assoc();

// Recent activities
$recent_activities = [];
$result = $conn->query("
    SELECT 'absensi' as type, s.nama_lengkap, a.tanggal as date, a.keterangan as detail
    FROM absensi a
    JOIN siswa s ON a.siswa_nisn = s.nisn
    ORDER BY a.created_at DESC
    LIMIT 5
");
while ($row = $result->fetch_assoc()) {
    $recent_activities[] = $row;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-card {
            border-left: 4px solid;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stats-card.siswa { border-left-color: #007bff; }
        .stats-card.kelas { border-left-color: #28a745; }
        .stats-card.absensi { border-left-color: #17a2b8; }
        .stats-card.catatan { border-left-color: #ffc107; }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .activity-item {
            border-left: 3px solid #007bff;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Card -->
        <div class="card welcome-card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1">Selamat Datang, <?= ucfirst(getUserRole()) ?>!</h2>
                        <p class="mb-0">Kelola data siswa dengan mudah dan efisien</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-user-graduate fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card siswa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title text-muted mb-1">Total Siswa</h6>
                                <h2 class="text-primary mb-0"><?= $stats['total_siswa'] ?></h2>
                            </div>
                            <div>
                                <i class="fas fa-users fa-2x text-primary opacity-75"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="../siswa/index.php" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card kelas">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title text-muted mb-1">Total Kelas</h6>
                                <h2 class="text-success mb-0"><?= $stats['total_kelas'] ?></h2>
                            </div>
                            <div>
                                <i class="fas fa-school fa-2x text-success opacity-75"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="../kelas/index.php" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card absensi">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title text-muted mb-1">Absensi Hari Ini</h6>
                                <h2 class="text-info mb-0"><?= $stats['absensi_hari_ini'] ?></h2>
                            </div>
                            <div>
                                <i class="fas fa-calendar-check fa-2x text-info opacity-75"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="../absensi/index.php" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card catatan">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title text-muted mb-1">Total Catatan</h6>
                                <h2 class="text-warning mb-0"><?= $stats['total_catatan'] ?></h2>
                            </div>
                            <div>
                                <i class="fas fa-sticky-note fa-2x text-warning opacity-75"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="../catatan/index.php" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Activities -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Statistik Absensi Bulan Ini</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h4 class="text-success"><?= $absensi_stats['hadir'] ?></h4>
                                    <small class="text-muted">Hadir</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h4 class="text-danger"><?= $absensi_stats['sakit'] ?></h4>
                                    <small class="text-muted">Sakit</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="p-3 bg-warning bg-opacity-10 rounded">
                                    <h4 class="text-warning"><?= $absensi_stats['izin'] ?></h4>
                                    <small class="text-muted">Izin</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h4 class="text-secondary"><?= $absensi_stats['alpa'] ?></h4>
                                    <small class="text-muted">Alpha</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Aktivitas Terbaru</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?= htmlspecialchars($activity['nama_lengkap']) ?></strong>
                                    <small class="text-muted"><?= date('d/m', strtotime($activity['date'])) ?></small>
                                </div>
                                <small class="text-muted">
                                    Absensi: <span class="badge bg-<?= $activity['detail'] == 'Hadir' ? 'success' : ($activity['detail'] == 'Sakit' ? 'danger' : ($activity['detail'] == 'Izin' ? 'warning' : 'secondary')) ?> badge-sm">
                                        <?= $activity['detail'] ?>
                                    </span>
                                </small>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted text-center">Belum ada aktivitas</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Aksi Cepat</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="../siswa/tambah.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-user-plus me-2"></i>Tambah Siswa
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../absensi/tambah.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-calendar-plus me-2"></i>Input Absensi
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../catatan/tambah.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-sticky-note me-2"></i>Tambah Catatan
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../berkas/index.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-folder-plus me-2"></i>Kelola Berkas
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>