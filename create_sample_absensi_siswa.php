<?php
require_once 'config/db.php';

echo "=== MEMBUAT DATA SAMPLE ABSENSI PER SISWA ===\n";

// Data sample absensi untuk siswa tertentu
$sample_absensi = [
    // <PERSON> (0076116641)
    ['0076116641', '2025-07-01', 'Hadir', '07:15:00', '15:30:00', null],
    ['0076116641', '2025-07-02', 'Hadir', '07:10:00', '15:25:00', null],
    ['0076116641', '2025-07-03', 'Sakit', null, null, '<PERSON><PERSON><PERSON> tinggi'],
    ['0076116641', '2025-07-04', 'Hadir', '07:20:00', '15:35:00', null],
    ['0076116641', '2025-07-05', 'Hadir', '07:05:00', '15:30:00', null],
    
    // <PERSON><PERSON> (0076116642)
    ['0076116642', '2025-07-01', 'Hadir', '07:12:00', '15:28:00', null],
    ['0076116642', '2025-07-02', 'Izin', null, null, 'Keperluan keluarga'],
    ['0076116642', '2025-07-03', 'Hadir', '07:18:00', '15:32:00', null],
    ['0076116642', '2025-07-04', 'Hadir', '07:08:00', '15:30:00', null],
    ['0076116642', '2025-07-05', 'Alpa', null, null, 'Tidak ada keterangan'],
    
    // Budi Santoso (0076116643)
    ['0076116643', '2025-07-01', 'Hadir', '07:25:00', '15:40:00', null],
    ['0076116643', '2025-07-02', 'Hadir', '07:15:00', '15:30:00', null],
    ['0076116643', '2025-07-03', 'Hadir', '07:10:00', '15:25:00', null],
    ['0076116643', '2025-07-04', 'Sakit', null, null, 'Flu dan batuk'],
    ['0076116643', '2025-07-05', 'Hadir', '07:20:00', '15:35:00', null],
];

echo "Menambahkan data sample absensi...\n";

foreach ($sample_absensi as $absensi) {
    $siswa_nisn = $absensi[0];
    $tanggal = $absensi[1];
    $keterangan = $absensi[2];
    $jam_masuk = $absensi[3];
    $jam_keluar = $absensi[4];
    $catatan = $absensi[5];
    
    // Cek apakah data sudah ada
    $check_stmt = $conn->prepare("SELECT id FROM absensi WHERE siswa_nisn = ? AND tanggal = ?");
    $check_stmt->bind_param("ss", $siswa_nisn, $tanggal);
    $check_stmt->execute();
    $existing = $check_stmt->get_result()->fetch_assoc();
    
    if (!$existing) {
        $stmt = $conn->prepare("INSERT INTO absensi (siswa_nisn, tanggal, keterangan, jam_masuk, jam_keluar, catatan) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $siswa_nisn, $tanggal, $keterangan, $jam_masuk, $jam_keluar, $catatan);
        
        if ($stmt->execute()) {
            echo "✓ Absensi $siswa_nisn - $tanggal ($keterangan) berhasil ditambahkan\n";
        } else {
            echo "✗ Error menambahkan absensi $siswa_nisn - $tanggal: " . $conn->error . "\n";
        }
    } else {
        echo "Absensi $siswa_nisn - $tanggal sudah ada\n";
    }
}

// Update statistik absensi
echo "\nMemperbarui statistik absensi...\n";

$siswa_list = ['0076116641', '0076116642', '0076116643'];
$bulan = 7;
$tahun = 2025;

foreach ($siswa_list as $nisn) {
    // Hitung statistik
    $stats_query = "
        SELECT
            COUNT(*) as total_hari,
            SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
            SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
            SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as total_izin,
            SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as total_alpa
        FROM absensi
        WHERE siswa_nisn = ? AND MONTH(tanggal) = ? AND YEAR(tanggal) = ?
    ";
    
    $stats_stmt = $conn->prepare($stats_query);
    $stats_stmt->bind_param("sii", $nisn, $bulan, $tahun);
    $stats_stmt->execute();
    $stats = $stats_stmt->get_result()->fetch_assoc();
    
    $total_hari = $stats['total_hari'] ?: 1;
    $persentase = round(($stats['total_hadir'] / $total_hari) * 100, 2);
    
    // Insert atau update statistik
    $check_stats = $conn->prepare("SELECT id FROM statistik_absensi WHERE siswa_nisn = ? AND bulan = ? AND tahun = ?");
    $check_stats->bind_param("sii", $nisn, $bulan, $tahun);
    $check_stats->execute();
    $existing_stats = $check_stats->get_result()->fetch_assoc();
    
    if ($existing_stats) {
        $update_stats = $conn->prepare("
            UPDATE statistik_absensi SET
            total_hadir = ?, total_sakit = ?, total_izin = ?, total_alpa = ?, persentase_kehadiran = ?
            WHERE siswa_nisn = ? AND bulan = ? AND tahun = ?
        ");
        $update_stats->bind_param("iiiidsii", 
            $stats['total_hadir'], $stats['total_sakit'], $stats['total_izin'], $stats['total_alpa'], 
            $persentase, $nisn, $bulan, $tahun
        );
        $update_stats->execute();
        echo "✓ Statistik $nisn diperbarui (Hadir: {$stats['total_hadir']}, Persentase: $persentase%)\n";
    } else {
        $insert_stats = $conn->prepare("
            INSERT INTO statistik_absensi (siswa_nisn, bulan, tahun, total_hadir, total_sakit, total_izin, total_alpa, persentase_kehadiran)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $insert_stats->bind_param("siiiiiid", 
            $nisn, $bulan, $tahun, $stats['total_hadir'], $stats['total_sakit'], $stats['total_izin'], $stats['total_alpa'], $persentase
        );
        $insert_stats->execute();
        echo "✓ Statistik $nisn dibuat (Hadir: {$stats['total_hadir']}, Persentase: $persentase%)\n";
    }
}

echo "\n=== RINGKASAN DATA ABSENSI ===\n";

// Tampilkan ringkasan per siswa
foreach ($siswa_list as $nisn) {
    $siswa_query = $conn->prepare("SELECT nama_lengkap FROM siswa WHERE nisn = ?");
    $siswa_query->bind_param("s", $nisn);
    $siswa_query->execute();
    $siswa_data = $siswa_query->get_result()->fetch_assoc();
    
    $count_query = $conn->prepare("SELECT COUNT(*) as total FROM absensi WHERE siswa_nisn = ?");
    $count_query->bind_param("s", $nisn);
    $count_query->execute();
    $count = $count_query->get_result()->fetch_assoc()['total'];
    
    echo "- " . ($siswa_data['nama_lengkap'] ?? 'Unknown') . " ($nisn): $count data absensi\n";
}

echo "\n🎉 Data sample absensi per siswa berhasil dibuat!\n";
echo "Akses halaman: views/siswa/absensi.php?nisn=0076116641\n";

$conn->close();
?>
