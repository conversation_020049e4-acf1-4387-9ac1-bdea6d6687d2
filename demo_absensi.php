<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Absensi - SISWA APP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .badge-hadir { background-color: #28a745; }
        .badge-sakit { background-color: #dc3545; }
        .badge-izin { background-color: #ffc107; color: #000; }
        .badge-alpa { background-color: #6c757d; }
        .stats-card {
            border-left: 4px solid;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .stats-hadir { border-left-color: #28a745; }
        .stats-sakit { border-left-color: #dc3545; }
        .stats-izin { border-left-color: #ffc107; }
        .stats-alpa { border-left-color: #6c757d; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Data Siswa</a>
                <a class="nav-link" href="#">Data Kelas</a>
                <a class="nav-link active" href="#">Data Absensi</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-calendar-check me-2"></i>Data Absensi</h4>
                <small class="text-muted">Kelola absensi siswa harian</small>
            </div>
            <div>
                <button class="btn btn-success btn-sm me-2" onclick="showTambahAbsensi()">
                    <i class="fas fa-plus"></i> Tambah Absensi
                </button>
                <button class="btn btn-info btn-sm" onclick="showLaporanAbsensi()">
                    <i class="fas fa-chart-bar"></i> Laporan
                </button>
            </div>
        </div>

        <!-- Statistik Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card stats-hadir">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Hadir Hari Ini</h6>
                                <h3 class="text-success">45</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-sakit">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Sakit</h6>
                                <h3 class="text-danger">3</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-thermometer-half fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-izin">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Izin</h6>
                                <h3 class="text-warning">2</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-hand-paper fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card stats-alpa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Alpa</h6>
                                <h3 class="text-secondary">1</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x text-secondary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter dan Search -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Tanggal</label>
                        <input type="date" class="form-control" value="2025-07-05">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Kelas</label>
                        <select class="form-select">
                            <option value="">Semua Kelas</option>
                            <option value="KPP">Kelas KPP</option>
                            <option value="X">Kelas X</option>
                            <option value="XI">Kelas XI</option>
                            <option value="XII">Kelas XII</option>
                            <option value="KPA">Kelas KPA</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select class="form-select">
                            <option value="">Semua Status</option>
                            <option value="Hadir">Hadir</option>
                            <option value="Sakit">Sakit</option>
                            <option value="Izin">Izin</option>
                            <option value="Alpa">Alpa</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Cari Siswa</label>
                        <input type="text" class="form-control" placeholder="Nama atau NISN">
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <div class="alert alert-success alert-dismissible fade show" id="successAlert" style="display: none;">
            <i class="fas fa-check-circle me-2"></i><span id="successMessage"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Data Table -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-table me-2"></i>Data Absensi - 05 Juli 2025</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Siswa</th>
                                <th>Kelas</th>
                                <th>Status</th>
                                <th>Jam Masuk</th>
                                <th>Jam Keluar</th>
                                <th>Catatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <strong>Ahmad Rizki Pratama</strong><br>
                                    <small class="text-muted">NISN: 0076116641</small>
                                </td>
                                <td><span class="badge bg-primary">Kelas X</span></td>
                                <td><span class="badge badge-hadir">Hadir</span></td>
                                <td>07:15</td>
                                <td>15:30</td>
                                <td>-</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" title="Edit" onclick="editAbsensi('1', 'Ahmad Rizki Pratama', 'Hadir')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Hapus" onclick="hapusAbsensi('1', 'Ahmad Rizki Pratama')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>
                                    <strong>Siti Nurhaliza</strong><br>
                                    <small class="text-muted">NISN: 0076116642</small>
                                </td>
                                <td><span class="badge bg-primary">Kelas X</span></td>
                                <td><span class="badge badge-sakit">Sakit</span></td>
                                <td>-</td>
                                <td>-</td>
                                <td>Demam tinggi</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" title="Edit" onclick="editAbsensi('2', 'Siti Nurhaliza', 'Sakit')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Hapus" onclick="hapusAbsensi('2', 'Siti Nurhaliza')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>
                                    <strong>Budi Santoso</strong><br>
                                    <small class="text-muted">NISN: 0076116643</small>
                                </td>
                                <td><span class="badge bg-success">Kelas XI</span></td>
                                <td><span class="badge badge-izin">Izin</span></td>
                                <td>-</td>
                                <td>-</td>
                                <td>Keperluan keluarga</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" title="Edit" onclick="editAbsensi('3', 'Budi Santoso', 'Izin')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Hapus" onclick="hapusAbsensi('3', 'Budi Santoso')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showTambahAbsensi() {
            Swal.fire({
                title: 'Tambah Absensi',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Pilih Siswa</label>
                            <select class="form-select" id="siswaSelect">
                                <option value="">Pilih Siswa</option>
                                <option value="0076116641">Ahmad Rizki Pratama (0076116641)</option>
                                <option value="0076116642">Siti Nurhaliza (0076116642)</option>
                                <option value="0076116643">Budi Santoso (0076116643)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="tanggalAbsensi" value="2025-07-05">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status Kehadiran</label>
                            <select class="form-select" id="statusAbsensi">
                                <option value="Hadir">Hadir</option>
                                <option value="Sakit">Sakit</option>
                                <option value="Izin">Izin</option>
                                <option value="Alpa">Alpa</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Jam Masuk</label>
                                <input type="time" class="form-control" id="jamMasuk">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Jam Keluar</label>
                                <input type="time" class="form-control" id="jamKeluar">
                            </div>
                        </div>
                        <div class="mb-3 mt-3">
                            <label class="form-label">Catatan</label>
                            <textarea class="form-control" id="catatanAbsensi" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '500px'
            }).then((result) => {
                if (result.isConfirmed) {
                    showSuccessMessage('Absensi berhasil ditambahkan!');
                }
            });
        }

        function editAbsensi(id, nama, status) {
            Swal.fire({
                title: 'Edit Absensi',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Siswa</label>
                            <input type="text" class="form-control" value="${nama}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status Kehadiran</label>
                            <select class="form-select" id="editStatus">
                                <option value="Hadir" ${status === 'Hadir' ? 'selected' : ''}>Hadir</option>
                                <option value="Sakit" ${status === 'Sakit' ? 'selected' : ''}>Sakit</option>
                                <option value="Izin" ${status === 'Izin' ? 'selected' : ''}>Izin</option>
                                <option value="Alpa" ${status === 'Alpa' ? 'selected' : ''}>Alpa</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Jam Masuk</label>
                                <input type="time" class="form-control" id="editJamMasuk" value="07:15">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Jam Keluar</label>
                                <input type="time" class="form-control" id="editJamKeluar" value="15:30">
                            </div>
                        </div>
                        <div class="mb-3 mt-3">
                            <label class="form-label">Catatan</label>
                            <textarea class="form-control" id="editCatatan" rows="2"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Update',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#ffc107',
                cancelButtonColor: '#6c757d',
                width: '500px'
            }).then((result) => {
                if (result.isConfirmed) {
                    showSuccessMessage('Absensi berhasil diperbarui!');
                }
            });
        }

        function hapusAbsensi(id, nama) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                html: `Apakah Anda yakin ingin menghapus data absensi:<br><strong>${nama}</strong>?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash me-1"></i>Ya, Hapus!',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    showSuccessMessage('Data absensi berhasil dihapus!');
                }
            });
        }

        function showLaporanAbsensi() {
            Swal.fire({
                title: 'Laporan Absensi',
                html: `
                    <div class="text-start">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">Dari Tanggal</label>
                                <input type="date" class="form-control" value="2025-07-01">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Sampai Tanggal</label>
                                <input type="date" class="form-control" value="2025-07-05">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Kelas</label>
                            <select class="form-select">
                                <option value="">Semua Kelas</option>
                                <option value="KPP">Kelas KPP</option>
                                <option value="X">Kelas X</option>
                                <option value="XI">Kelas XI</option>
                                <option value="XII">Kelas XII</option>
                                <option value="KPA">Kelas KPA</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Format Laporan</label>
                            <select class="form-select">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-download me-1"></i>Download',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#17a2b8',
                cancelButtonColor: '#6c757d',
                width: '500px'
            }).then((result) => {
                if (result.isConfirmed) {
                    showSuccessMessage('Laporan absensi berhasil didownload!');
                }
            });
        }

        function showSuccessMessage(message) {
            const alert = document.getElementById('successAlert');
            const messageSpan = document.getElementById('successMessage');
            messageSpan.textContent = message;
            alert.style.display = 'block';
            alert.classList.add('show');
            
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 150);
            }, 3000);
        }
    </script>
</body>
</html>
