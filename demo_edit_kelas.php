<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Kelas - SISWA APP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .form-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
        }
        .btn-save {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
        }
        .btn-cancel {
            background: linear-gradient(45deg, #6c757d, #495057);
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link" href="#">Data Siswa</a>
                <a class="nav-link active" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-edit me-2"></i>Edit Kelas</h4>
                <small class="text-muted">Edit data kelas: Kelas X-1</small>
            </div>
            <a href="demo_kelas.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>

        <!-- Form -->
        <div class="form-container">
            <form id="formEditKelas">
                <div class="row">
                    <!-- Data Kelas -->
                    <div class="col-md-6">
                        <h5 class="section-title"><i class="fas fa-school me-2"></i>Data Kelas</h5>

                        <div class="mb-3">
                            <label class="form-label">Nama Kelas *</label>
                            <input type="text" class="form-control" name="nama_kelas" value="Kelas X-1" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kurikulum</label>
                            <select class="form-select" name="kurikulum">
                                <option value="">Pilih Kurikulum</option>
                                <option value="Kurikulum Merdeka" selected>Kurikulum Merdeka</option>
                                <option value="Kurikulum 2013">Kurikulum 2013</option>
                                <option value="KTSP">KTSP</option>
                            </select>
                            <div class="form-text">
                                <small>
                                    <strong>Kurikulum Merdeka:</strong> Kurikulum terbaru yang memberikan kebebasan kepada sekolah untuk mengembangkan pembelajaran sesuai dengan karakteristik siswa.<br>
                                    <strong>Kurikulum 2013:</strong> Kurikulum yang menekankan pada pengembangan kompetensi sikap, pengetahuan, dan keterampilan.<br>
                                    <strong>KTSP:</strong> Kurikulum Tingkat Satuan Pendidikan yang memberikan otonomi kepada sekolah dalam mengembangkan kurikulum.
                                </small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tahun Pelajaran *</label>
                            <input type="text" class="form-control" name="tahun_pelajaran" value="2024/2025" required>
                        </div>
                    </div>

                    <!-- Data Tambahan -->
                    <div class="col-md-6">
                        <h5 class="section-title"><i class="fas fa-cog me-2"></i>Pengaturan</h5>

                        <div class="mb-3">
                            <label class="form-label">Tingkat *</label>
                            <select class="form-select" name="tingkat" required>
                                <option value="">Pilih Tingkat</option>
                                <option value="KPA" selected>KPA (Kelas Percepatan Akses)</option>
                                <option value="X">Kelas X</option>
                                <option value="XI">Kelas XI</option>
                                <option value="XII">Kelas XII</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kapasitas</label>
                            <input type="number" class="form-control" name="kapasitas" value="30" min="1" max="50">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Wali Kelas</label>
                            <input type="text" class="form-control" name="wali_kelas" value="Y. Guntur Cahyo Dewantoro, ST.">
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="demo_kelas.php" class="btn btn-cancel text-white">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-save text-white">
                                <i class="fas fa-save me-2"></i>Simpan Perubahan
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.getElementById('formEditKelas').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const namaKelas = formData.get('nama_kelas');
            const kurikulum = formData.get('kurikulum');
            const tahunPelajaran = formData.get('tahun_pelajaran');
            const tingkat = formData.get('tingkat');
            const kapasitas = formData.get('kapasitas');
            const waliKelas = formData.get('wali_kelas');

            // Validate required fields
            if (!namaKelas || !tahunPelajaran || !tingkat) {
                Swal.fire({
                    title: 'Validasi Error',
                    text: 'Mohon lengkapi semua field yang wajib diisi!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'Menyimpan Perubahan...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Simulate save process
            setTimeout(() => {
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Data kelas berhasil diperbarui',
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Redirect to kelas list
                    window.location.href = 'demo_kelas.php?updated=1';
                });
            }, 2000);
        });

        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('input[name="nama_kelas"]').focus();
        });
    </script>
</body>
</html>
