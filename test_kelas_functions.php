<?php
// Test file untuk fungsi kelas
session_start();
$_SESSION['user_id'] = 1; // Set session untuk testing

require_once 'config/db.php';

echo "<h2>Test Fungsi Kelas</h2>";

// Test 1: Cek koneksi database
echo "<h3>1. Test Koneksi Database</h3>";
if ($conn) {
    echo "✅ Koneksi database berhasil<br>";
} else {
    echo "❌ Koneksi database gagal<br>";
}

// Test 2: Cek tabel kelas
echo "<h3>2. Test Tabel Kelas</h3>";
$result = $conn->query("SHOW TABLES LIKE 'kelas'");
if ($result->num_rows > 0) {
    echo "✅ Tabel kelas ada<br>";
    
    // Cek struktur tabel
    $columns = $conn->query("DESCRIBE kelas");
    echo "<strong>Kolom tabel kelas:</strong><br>";
    while ($col = $columns->fetch_assoc()) {
        echo "- " . $col['Field'] . " (" . $col['Type'] . ")<br>";
    }
} else {
    echo "❌ Tabel kelas tidak ada<br>";
}

// Test 3: Cek data kelas
echo "<h3>3. Test Data Kelas</h3>";
$kelas_result = $conn->query("SELECT COUNT(*) as total FROM kelas");
$total = $kelas_result->fetch_assoc()['total'];
echo "Total kelas: <strong>$total</strong><br>";

if ($total > 0) {
    echo "<strong>Data kelas:</strong><br>";
    $data_kelas = $conn->query("SELECT id, nama_kelas, tingkat, kurikulum FROM kelas LIMIT 5");
    while ($kelas = $data_kelas->fetch_assoc()) {
        echo "- " . $kelas['nama_kelas'] . " (ID: " . $kelas['id'] . ", Tingkat: " . ($kelas['tingkat'] ?? 'N/A') . ")<br>";
    }
}

// Test 4: Test link edit dan delete
echo "<h3>4. Test Link Edit dan Delete</h3>";
if ($total > 0) {
    $first_kelas = $conn->query("SELECT id, nama_kelas FROM kelas LIMIT 1")->fetch_assoc();
    $id = $first_kelas['id'];
    $nama = $first_kelas['nama_kelas'];
    
    echo "<strong>Contoh kelas:</strong> $nama (ID: $id)<br>";
    echo "<strong>Link Edit:</strong> <a href='views/kelas/edit.php?id=$id' target='_blank'>Edit $nama</a><br>";
    echo "<strong>Link Delete:</strong> <a href='controllers/KelasController.php?hapus=$id' onclick='return confirm(\"Yakin hapus?\")'>Hapus $nama</a><br>";
} else {
    echo "Tidak ada data kelas untuk ditest<br>";
}

// Test 5: Test controller files
echo "<h3>5. Test Controller Files</h3>";
$controller_file = 'controllers/KelasController.php';
if (file_exists($controller_file)) {
    echo "✅ File $controller_file ada<br>";
} else {
    echo "❌ File $controller_file tidak ada<br>";
}

$edit_file = 'views/kelas/edit.php';
if (file_exists($edit_file)) {
    echo "✅ File $edit_file ada<br>";
} else {
    echo "❌ File $edit_file tidak ada<br>";
}

// Test 6: Test relasi dengan siswa
echo "<h3>6. Test Relasi dengan Siswa</h3>";
$relasi_result = $conn->query("
    SELECT k.nama_kelas, COUNT(s.nisn) as jumlah_siswa 
    FROM kelas k 
    LEFT JOIN siswa s ON k.id = s.kelas_id 
    GROUP BY k.id, k.nama_kelas
");

if ($relasi_result->num_rows > 0) {
    echo "<strong>Jumlah siswa per kelas:</strong><br>";
    while ($row = $relasi_result->fetch_assoc()) {
        echo "- " . $row['nama_kelas'] . ": " . $row['jumlah_siswa'] . " siswa<br>";
    }
} else {
    echo "Tidak ada data relasi kelas-siswa<br>";
}

echo "<hr>";
echo "<p><a href='views/kelas/index.php'>Kembali ke Data Kelas</a></p>";
echo "<p><a href='demo_kelas.php'>Lihat Demo Kelas</a></p>";
?>
