<?php
session_start();
require_once '../../config/db.php';

$id = $_GET['id'] ?? '';
$query = "SELECT * FROM kelas WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $id);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

if (!$kelas) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-label {
            font-weight: 500;
            color: #495057;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Data Siswa</a>
                <a class="nav-link active" href="index.php">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-edit me-2"></i>Edit Kelas</h4>
                <small class="text-muted">Edit data kelas: <?php echo htmlspecialchars($kelas['nama_kelas']); ?></small>
            </div>
            <a href="detail.php?id=<?php echo $kelas['id']; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali
            </a>
        </div>

        <!-- Form -->
        <div class="form-container">
            <form action="../../controllers/KelasController.php" method="POST">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" value="<?php echo $kelas['id']; ?>">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Nama Kelas *</label>
                            <input type="text" class="form-control" name="nama_kelas" value="<?php echo htmlspecialchars($kelas['nama_kelas']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Kurikulum</label>
                            <select class="form-select" name="kurikulum">
                                <option value="">Pilih Kurikulum</option>
                                <option value="Kurikulum Seminal" <?php echo ($kelas['kurikulum'] == 'Kurikulum Seminal') ? 'selected' : ''; ?>>Kurikulum Seminal</option>
                                <option value="Kurikulum K13" <?php echo ($kelas['kurikulum'] == 'Kurikulum K13') ? 'selected' : ''; ?>>Kurikulum K13</option>
                                <option value="Kurikulum Merdeka" <?php echo ($kelas['kurikulum'] == 'Kurikulum Merdeka') ? 'selected' : ''; ?>>Kurikulum Merdeka</option>
                                <option value="Kurikulum Deep Learning" <?php echo ($kelas['kurikulum'] == 'Kurikulum Deep Learning') ? 'selected' : ''; ?>>Kurikulum Deep Learning</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Rilis Kurikulum</label>
                            <input type="text" class="form-control" name="rilis_kurikulum" value="<?php echo htmlspecialchars($kelas['rilis_kurikulum'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Tingkat *</label>
                            <select class="form-select" name="tingkat" required>
                                <option value="">Pilih Tingkat</option>
                                <option value="KPA (Kelas Percepatan Akses)" <?php echo ($kelas['tingkat'] == 'KPA (Kelas Percepatan Akses)') ? 'selected' : ''; ?>>KPA (Kelas Percepatan Akses)</option>
                                <option value="KPP (Kelas Percepatan Sertifikasi)" <?php echo ($kelas['tingkat'] == 'KPP (Kelas Percepatan Sertifikasi)') ? 'selected' : ''; ?>>KPP (Kelas Percepatan Sertifikasi)</option>
                                <option value="X (Kelas 10)" <?php echo ($kelas['tingkat'] == 'X (Kelas 10)') ? 'selected' : ''; ?>>X (Kelas 10)</option>
                                <option value="XI (Kelas 11)" <?php echo ($kelas['tingkat'] == 'XI (Kelas 11)') ? 'selected' : ''; ?>>XI (Kelas 11)</option>
                                <option value="XII (Kelas 12)" <?php echo ($kelas['tingkat'] == 'XII (Kelas 12)') ? 'selected' : ''; ?>>XII (Kelas 12)</option>
                                <option value="KPA (Kelas Percepatan Akses)" <?php echo ($kelas['tingkat'] == 'KPA (Kelas Percepatan Akses)') ? 'selected' : ''; ?>>KPA (Kelas Percepatan Akses)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Tahun Pelajaran *</label>
                            <input type="text" class="form-control" name="tahun_pelajaran" value="<?php echo htmlspecialchars($kelas['tahun_pelajaran'] ?? '2024/2025'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Kapasitas</label>
                            <input type="number" class="form-control" name="kapasitas" value="<?php echo $kelas['kapasitas'] ?? 30; ?>" min="1" max="50">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Wali Kelas</label>
                            <input type="text" class="form-control" name="wali_kelas" value="<?php echo htmlspecialchars($kelas['wali_kelas'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="detail.php?id=<?php echo $kelas['id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Perubahan
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
