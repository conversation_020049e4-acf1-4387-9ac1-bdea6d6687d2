<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit;
}

require_once '../../config/db.php';
require_once '../../models/Catatan.php';

$nisn = $_GET['nisn'] ?? '';
if (empty($nisn)) {
    header("Location: index.php");
    exit;
}

// Get data siswa
$stmt = $conn->prepare("
    SELECT s.*, k.nama_kelas 
    FROM siswa s 
    LEFT JOIN kelas k ON s.kelas_id = k.id 
    WHERE s.nisn = ?
");
$stmt->bind_param("s", $nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    header("Location: index.php?error=siswa_not_found");
    exit;
}

// Get statistik absensi bulan ini
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_absensi,
        SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as hadir,
        SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as sakit,
        SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as izin,
        SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as alpa
    FROM absensi 
    WHERE siswa_nisn = ? 
    AND MONTH(tanggal) = MONTH(CURRENT_DATE()) 
    AND YEAR(tanggal) = YEAR(CURRENT_DATE())
");
$stmt->bind_param("s", $nisn);
$stmt->execute();
$absensi_stats = $stmt->get_result()->fetch_assoc();

// Hitung persentase kehadiran
$total_hari = $absensi_stats['total_absensi'] ?: 1;
$persentase_kehadiran = round(($absensi_stats['hadir'] / $total_hari) * 100, 1);

// Get statistik catatan
$catatan_model = new Catatan($conn);
$catatan_stats = $catatan_model->getStatistikCatatan($nisn);

// Get berkas siswa
$stmt = $conn->prepare("SELECT * FROM berkas_siswa WHERE siswa_nisn = ? ORDER BY uploaded_at DESC");
$stmt->bind_param("s", $nisn);
$stmt->execute();
$berkas_list = $stmt->get_result();

$active_tab = $_GET['tab'] ?? 'personal';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Siswa - <?= htmlspecialchars($siswa['nama_lengkap']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            object-fit: cover;
        }
        .stats-card {
            border-left: 4px solid;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .stats-card.kehadiran { border-left-color: #28a745; }
        .stats-card.sakit { border-left-color: #dc3545; }
        .stats-card.izin { border-left-color: #ffc107; }
        .stats-card.alpha { border-left-color: #6c757d; }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background: #007bff;
            color: white;
            border-radius: 8px 8px 0 0;
        }
        .tab-content {
            background: white;
            border-radius: 0 8px 8px 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link active" href="index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Profile Header -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <?php if ($siswa['foto']): ?>
                        <img src="../../uploads/foto/<?= $siswa['foto'] ?>" alt="Foto Siswa" class="profile-img">
                    <?php else: ?>
                        <div class="profile-img bg-secondary d-flex align-items-center justify-content-center">
                            <i class="fas fa-user fa-3x"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-1"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h2>
                    <p class="mb-1"><i class="fas fa-id-card me-2"></i>NISN: <?= $siswa['nisn'] ?></p>
                    <p class="mb-1"><i class="fas fa-school me-2"></i>Kelas: <?= $siswa['nama_kelas'] ?? 'Belum ada kelas' ?></p>
                    <p class="mb-0">
                        <span class="badge bg-<?= $siswa['status'] == 'Aktif' ? 'success' : 'secondary' ?> fs-6">
                            <?= $siswa['status'] ?>
                        </span>
                    </p>
                </div>
                <div class="col-md-3 text-end">
                    <a href="edit.php?nisn=<?= $siswa['nisn'] ?>" class="btn btn-light btn-lg">
                        <i class="fas fa-edit me-2"></i>Edit Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../dashboard/index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Data Siswa</a></li>
                <li class="breadcrumb-item active"><?= htmlspecialchars($siswa['nama_lengkap']) ?></li>
            </ol>
        </nav>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="siswaTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $active_tab == 'personal' ? 'active' : '' ?>" 
                        id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" 
                        type="button" role="tab">
                    <i class="fas fa-user me-2"></i>Informasi Personal
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $active_tab == 'absensi' ? 'active' : '' ?>" 
                        id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" 
                        type="button" role="tab">
                    <i class="fas fa-calendar-check me-2"></i>Data Absensi
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $active_tab == 'akademik' ? 'active' : '' ?>" 
                        id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" 
                        type="button" role="tab">
                    <i class="fas fa-book me-2"></i>Informasi Akademik
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $active_tab == 'catatan' ? 'active' : '' ?>" 
                        id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" 
                        type="button" role="tab">
                    <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $active_tab == 'berkas' ? 'active' : '' ?>" 
                        id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" 
                        type="button" role="tab">
                    <i class="fas fa-folder me-2"></i>Berkas Siswa
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content p-4" id="siswaTabContent">
            <!-- Tab Informasi Personal -->
            <div class="tab-pane fade <?= $active_tab == 'personal' ? 'show active' : '' ?>" 
                 id="personal" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-user-circle me-2"></i>Data Identitas</h5>
                        <div class="info-item d-flex">
                            <span class="info-label">NISN:</span>
                            <span><?= $siswa['nisn'] ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">NIK:</span>
                            <span><?= $siswa['nik'] ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">No. KK:</span>
                            <span><?= $siswa['no_kk'] ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Nama Lengkap:</span>
                            <span><?= htmlspecialchars($siswa['nama_lengkap']) ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Tempat Lahir:</span>
                            <span><?= htmlspecialchars($siswa['tempat_lahir']) ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Tanggal Lahir:</span>
                            <span><?= $siswa['tanggal_lahir'] ? date('d F Y', strtotime($siswa['tanggal_lahir'])) : 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Jenis Kelamin:</span>
                            <span><?= $siswa['jenis_kelamin'] == 'L' ? 'Laki-laki' : 'Perempuan' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Golongan Darah:</span>
                            <span><?= $siswa['golongan_darah'] ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Agama:</span>
                            <span><?= $siswa['agama'] ?: 'Tidak ada' ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-home me-2"></i>Data Kelahiran</h5>
                        <div class="info-item d-flex">
                            <span class="info-label">Tempat Lahir:</span>
                            <span><?= htmlspecialchars($siswa['tempat_lahir']) ?: 'Gunungkidul' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Tanggal Lahir:</span>
                            <span><?= $siswa['tanggal_lahir'] ? date('d F Y', strtotime($siswa['tanggal_lahir'])) : '08 Agustus 2007 (17 tahun)' ?></span>
                        </div>
                        
                        <h5 class="mb-3 mt-4"><i class="fas fa-phone me-2"></i>Kontak</h5>
                        <div class="info-item d-flex">
                            <span class="info-label">No. Telepon:</span>
                            <span><?= $siswa['no_telepon'] ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Email:</span>
                            <span><?= $siswa['email'] ?: 'Tidak ada' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Alamat:</span>
                            <span><?= htmlspecialchars($siswa['alamat']) ?: 'Tidak ada' ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Data Absensi -->
            <div class="tab-pane fade <?= $active_tab == 'absensi' ? 'show active' : '' ?>" 
                 id="absensi" role="tabpanel">
                <!-- Statistik Absensi -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card kehadiran">
                            <div class="card-body text-center">
                                <h3 class="text-success"><?= $persentase_kehadiran ?>%</h3>
                                <p class="mb-0">Kehadiran</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card sakit">
                            <div class="card-body text-center">
                                <h3 class="text-danger"><?= $absensi_stats['sakit'] ?></h3>
                                <p class="mb-0">Sakit</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card izin">
                            <div class="card-body text-center">
                                <h3 class="text-warning"><?= $absensi_stats['izin'] ?></h3>
                                <p class="mb-0">Izin</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card alpha">
                            <div class="card-body text-center">
                                <h3 class="text-secondary"><?= $absensi_stats['alpa'] ?></h3>
                                <p class="mb-0">Alpha</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Riwayat Absensi</h5>
                    <a href="../absensi/tambah.php?siswa_nisn=<?= $siswa['nisn'] ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Tambah Absensi
                    </a>
                </div>

                <div id="absensiContent">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Memuat data absensi...</p>
                    </div>
                </div>
            </div>

            <!-- Tab Informasi Akademik -->
            <div class="tab-pane fade <?= $active_tab == 'akademik' ? 'show active' : '' ?>" 
                 id="akademik" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-school me-2"></i>Informasi Sekolah</h5>
                        <div class="info-item d-flex">
                            <span class="info-label">Kelas:</span>
                            <span><?= $siswa['nama_kelas'] ?? 'Belum ada kelas' ?></span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Status:</span>
                            <span>
                                <span class="badge bg-<?= $siswa['status'] == 'Aktif' ? 'success' : 'secondary' ?>">
                                    <?= $siswa['status'] ?>
                                </span>
                            </span>
                        </div>
                        <div class="info-item d-flex">
                            <span class="info-label">Tanggal Daftar:</span>
                            <span><?= date('d F Y', strtotime($siswa['created_at'])) ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Statistik Akademik</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Fitur statistik akademik akan segera tersedia.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Catatan Siswa -->
            <div class="tab-pane fade <?= $active_tab == 'catatan' ? 'show active' : '' ?>" 
                 id="catatan" role="tabpanel">
                <!-- Statistik Catatan -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h3 class="text-primary"><?= $catatan_stats['total_catatan'] ?></h3>
                                <p class="mb-0">Total Catatan</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h3 class="text-success"><?= $catatan_stats['selesai'] ?></h3>
                                <p class="mb-0">Selesai</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h3 class="text-warning"><?= $catatan_stats['ditunda'] ?></h3>
                                <p class="mb-0">Ditunda</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h3 class="text-info"><?= $catatan_stats['aktif'] ?></h3>
                                <p class="mb-0">Aktif</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Catatan Siswa</h5>
                    <a href="../catatan/tambah.php?siswa_nisn=<?= $siswa['nisn'] ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Tambah Catatan Pertama
                    </a>
                </div>

                <div id="catatanContent">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Memuat catatan siswa...</p>
                    </div>
                </div>
            </div>

            <!-- Tab Berkas Siswa -->
            <div class="tab-pane fade <?= $active_tab == 'berkas' ? 'show active' : '' ?>" 
                 id="berkas" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Berkas Siswa</h5>
                    <a href="../berkas/upload.php?siswa_nisn=<?= $siswa['nisn'] ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-upload me-1"></i>Upload Berkas
                    </a>
                </div>

                <?php if ($berkas_list->num_rows > 0): ?>
                <div class="row">
                    <?php while ($berkas = $berkas_list->fetch_assoc()): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title"><?= htmlspecialchars($berkas['jenis_berkas']) ?></h6>
                                <p class="card-text small text-muted">
                                    <?= htmlspecialchars($berkas['nama_file']) ?><br>
                                    Upload: <?= date('d/m/Y', strtotime($berkas['uploaded_at'])) ?>
                                </p>
                                <div class="btn-group btn-group-sm w-100">
                                    <a href="../../uploads/<?= $berkas['file_path'] ?>" target="_blank" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> Lihat
                                    </a>
                                    <a href="../../uploads/<?= $berkas['file_path'] ?>" download class="btn btn-success">
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada berkas</h5>
                    <p class="text-muted">Siswa ini belum memiliki berkas yang diupload.</p>
                    <a href="../berkas/upload.php?siswa_nisn=<?= $siswa['nisn'] ?>" class="btn btn-success">
                        <i class="fas fa-upload me-1"></i>Upload Berkas Pertama
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load absensi data when tab is activated
        document.getElementById('absensi-tab').addEventListener('shown.bs.tab', function() {
            loadAbsensiData();
        });

        // Load catatan data when tab is activated
        document.getElementById('catatan-tab').addEventListener('shown.bs.tab', function() {
            loadCatatanData();
        });

        function loadAbsensiData() {
            fetch(`../../controllers/AbsensiController.php?get_absensi_siswa=1&siswa_nisn=<?= $siswa['nisn'] ?>`)
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    if (data.length > 0) {
                        html = '<div class="table-responsive"><table class="table table-striped">';
                        html += '<thead><tr><th>Tanggal</th><th>Keterangan</th><th>Catatan</th></tr></thead><tbody>';
                        data.forEach(item => {
                            html += `<tr>
                                <td>${item.tanggal}</td>
                                <td><span class="badge bg-${getBadgeColor(item.keterangan)}">${item.keterangan}</span></td>
                                <td>${item.catatan || '-'}</td>
                            </tr>`;
                        });
                        html += '</tbody></table></div>';
                    } else {
                        html = '<div class="text-center py-4"><p class="text-muted">Belum ada data absensi</p></div>';
                    }
                    document.getElementById('absensiContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('absensiContent').innerHTML = '<div class="alert alert-danger">Error loading data</div>';
                });
        }

        function loadCatatanData() {
            fetch(`../../controllers/CatatanController.php?get_catatan_siswa=1&siswa_nisn=<?= $siswa['nisn'] ?>`)
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    if (data.length > 0) {
                        data.forEach(item => {
                            html += `<div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="card-title">${item.judul_catatan}</h6>
                                        <span class="badge bg-${getStatusColor(item.status)}">${item.status}</span>
                                    </div>
                                    <p class="card-text">${item.isi_catatan}</p>
                                    <small class="text-muted">
                                        ${item.jenis_catatan} • ${item.created_at} • oleh ${item.created_by_name}
                                    </small>
                                </div>
                            </div>`;
                        });
                    } else {
                        html = '<div class="text-center py-4"><p class="text-muted">Belum ada catatan untuk siswa ini</p></div>';
                    }
                    document.getElementById('catatanContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('catatanContent').innerHTML = '<div class="alert alert-danger">Error loading data</div>';
                });
        }

        function getBadgeColor(keterangan) {
            switch(keterangan) {
                case 'Hadir': return 'success';
                case 'Sakit': return 'danger';
                case 'Izin': return 'warning';
                case 'Alpa': return 'secondary';
                default: return 'primary';
            }
        }

        function getStatusColor(status) {
            switch(status) {
                case 'Aktif': return 'info';
                case 'Selesai': return 'success';
                case 'Ditunda': return 'warning';
                case 'Dibatalkan': return 'secondary';
                default: return 'primary';
            }
        }

        // Load initial data if tabs are active
        if (document.getElementById('absensi').classList.contains('active')) {
            loadAbsensiData();
        }
        if (document.getElementById('catatan').classList.contains('active')) {
            loadCatatanData();
        }
    </script>
</body>
</html>
