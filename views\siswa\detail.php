<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login
requireLogin(true);

$nisn = $_GET['nisn'] ?? '';
$query = "SELECT s.*, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.nisn = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Data Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            height: fit-content;
        }
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: #6c757d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }
        .profile-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .profile-id {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        .action-btn {
            width: 100%;
            margin-bottom: 8px;
            text-align: left;
        }
        .btn-edit { background: #ffc107; border-color: #ffc107; }
        .btn-upload { background: #20c997; border-color: #20c997; }
        .btn-notes { background: #17a2b8; border-color: #17a2b8; }
        .btn-back { background: #6c757d; border-color: #6c757d; }

        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background: none;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .data-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .data-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }
        .data-content {
            flex: 1;
        }
        .data-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 2px;
        }
        .data-value {
            font-weight: 500;
            color: #333;
        }
        .bg-blue { background: #007bff; }
        .bg-green { background: #28a745; }
        .bg-orange { background: #fd7e14; }
        .bg-purple { background: #6f42c1; }
        .bg-teal { background: #20c997; }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link active" href="index.php">Data Siswa</a>
                <a class="nav-link" href="../kelas/index.php">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h4 class="mb-4">Preview Data Siswa</h4>

        <div class="row">
            <!-- Profile Card -->
            <div class="col-md-3">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    <div class="profile-name"><?php echo htmlspecialchars($siswa['nama_lengkap']); ?></div>
                    <div class="profile-id">NIS: A25.285</div>
                    <div class="profile-id">Kelas: <?php echo $siswa['nama_kelas'] ?? 'Belum ada kelas'; ?></div>

                    <hr>

                    <button class="btn btn-edit text-white action-btn" onclick="window.location.href='edit.php?nisn=<?php echo $siswa['nisn']; ?>'">
                        <i class="fas fa-edit me-2"></i>Edit Data Siswa
                    </button>
                    <button class="btn btn-upload text-white action-btn">
                        <i class="fas fa-upload me-2"></i>Upload Berkas
                    </button>
                    <button class="btn btn-notes text-white action-btn">
                        <i class="fas fa-sticky-note me-2"></i>Tambah Catatan
                    </button>
                    <button class="btn btn-back text-white action-btn" onclick="window.location.href='index.php'">
                        <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar
                    </button>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-md-9">
                <div class="content-card">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs mb-4" id="siswaTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab">
                                <i class="fas fa-user me-2"></i>Informasi Personal
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" type="button" role="tab">
                                <i class="fas fa-calendar-check me-2"></i>Data Absensi
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" type="button" role="tab">
                                <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" type="button" role="tab">
                                <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" type="button" role="tab">
                                <i class="fas fa-folder me-2"></i>Berkas Siswa
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="siswaTabContent">
                        <!-- Informasi Personal -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3"><i class="fas fa-id-card me-2"></i>Data Identitas</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-blue">
                                            <i class="fas fa-id-badge"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">NISN</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['nisn']); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-green">
                                            <i class="fas fa-id-card"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">NIK</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['nik'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-orange">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">No. KK</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['no_kk'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-purple">
                                            <i class="fas fa-tint"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Golongan Darah</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['golongan_darah'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="mb-3"><i class="fas fa-map-marker-alt me-2"></i>Data Kelahiran</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-teal">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Tempat Lahir</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['tempat_lahir'] ?? 'Gunungkidul'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-blue">
                                            <i class="fas fa-calendar"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Tanggal Lahir</div>
                                            <div class="data-value"><?php echo date('d F Y', strtotime($siswa['tanggal_lahir'] ?? '2007-08-08')); ?> (17 tahun)</div>
                                        </div>
                                    </div>

                                    <h6 class="mb-3 mt-4"><i class="fas fa-phone me-2"></i>Kontak</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-green">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">No. Telepon</div>
                                            <div class="data-value">Tidak ada</div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-orange">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Email</div>
                                            <div class="data-value">Tidak ada</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Absensi -->
                        <div class="tab-pane fade" id="absensi" role="tabpanel">
                            <?php
                            // Get statistik absensi bulan ini
                            $bulan_ini = date('n');
                            $tahun_ini = date('Y');

                            $stats_query = "
                                SELECT
                                    COUNT(*) as total_hari,
                                    SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
                                    SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
                                    SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as total_izin,
                                    SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as total_alpa
                                FROM absensi
                                WHERE siswa_nisn = ? AND MONTH(tanggal) = ? AND YEAR(tanggal) = ?
                            ";

                            $stats_stmt = $conn->prepare($stats_query);
                            $stats_stmt->bind_param("sii", $siswa['nisn'], $bulan_ini, $tahun_ini);
                            $stats_stmt->execute();
                            $stats = $stats_stmt->get_result()->fetch_assoc();

                            // Hitung persentase kehadiran
                            $total_hari = $stats['total_hari'] ?: 1;
                            $persentase_kehadiran = round(($stats['total_hadir'] / $total_hari) * 100, 1);

                            // Get riwayat absensi terbaru (hanya Sakit, Izin, Alpha)
                            $riwayat_query = "
                                SELECT * FROM absensi
                                WHERE siswa_nisn = ? AND keterangan IN ('Sakit', 'Izin', 'Alpa')
                                ORDER BY tanggal DESC
                                LIMIT 10
                            ";

                            $riwayat_stmt = $conn->prepare($riwayat_query);
                            $riwayat_stmt->bind_param("s", $siswa['nisn']);
                            $riwayat_stmt->execute();
                            $riwayat_absensi = $riwayat_stmt->get_result();
                            ?>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-calendar-check me-2"></i>Data Absensi</h6>
                                <button class="btn btn-primary btn-sm" onclick="tambahAbsensi()">
                                    <i class="fas fa-plus me-1"></i>Tambah Absensi
                                </button>
                            </div>

                            <!-- Statistik Absensi -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #28a745;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #4CAF50, #45a049);">
                                                    <i class="fas fa-check-circle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-success"><?= $persentase_kehadiran ?>%</h4>
                                            <small class="text-muted">Kehadiran</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #dc3545;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #f44336, #d32f2f);">
                                                    <i class="fas fa-thermometer-half text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-danger"><?= $stats['total_sakit'] ?></h4>
                                            <small class="text-muted">Sakit</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #ffc107;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #ff9800, #f57c00);">
                                                    <i class="fas fa-hand-paper text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-warning"><?= $stats['total_izin'] ?></h4>
                                            <small class="text-muted">Ijin</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #6c757d;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #9e9e9e, #757575);">
                                                    <i class="fas fa-times-circle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-secondary"><?= $stats['total_alpa'] ?></h4>
                                            <small class="text-muted">Alpha</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Riwayat Ketidakhadiran -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Ketidakhadiran</h6>
                                </div>
                                <div class="card-body">
                                    <?php if ($riwayat_absensi->num_rows > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Tanggal</th>
                                                        <th>Status</th>
                                                        <th>Catatan</th>
                                                        <th>Aksi</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php while ($row = $riwayat_absensi->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?= date('d/m/Y', strtotime($row['tanggal'])) ?></td>
                                                        <td>
                                                            <?php
                                                            $badge_class = '';
                                                            switch($row['keterangan']) {
                                                                case 'Sakit': $badge_class = 'bg-danger'; break;
                                                                case 'Izin': $badge_class = 'bg-warning text-dark'; break;
                                                                case 'Alpa': $badge_class = 'bg-secondary'; break;
                                                            }
                                                            ?>
                                                            <span class="badge <?= $badge_class ?>">
                                                                <?= $row['keterangan'] ?>
                                                            </span>
                                                        </td>
                                                        <td><?= htmlspecialchars($row['catatan'] ?? '-') ?></td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <button class="btn btn-outline-warning" title="Edit" onclick="editAbsensi('<?= $row['id'] ?>')">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-outline-danger" title="Hapus" onclick="hapusAbsensi('<?= $row['id'] ?>')">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <?php endwhile; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-calendar-check fa-3x text-success mb-3"></i>
                                            <h6 class="text-success">Tidak ada ketidakhadiran</h6>
                                            <p class="text-muted">Siswa ini tidak memiliki catatan sakit, izin, atau alpha</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Link ke Data Absensi Lengkap -->
                            <div class="text-center mt-3">
                                <a href="absensi.php?nisn=<?= $siswa['nisn'] ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>Lihat Data Absensi Lengkap
                                </a>
                            </div>
                        </div>

                        <!-- Informasi Akademik -->
                        <div class="tab-pane fade" id="akademik" role="tabpanel">
                            <h6><i class="fas fa-graduation-cap me-2"></i>Informasi Akademik</h6>
                            <p class="text-muted">Data akademik akan segera tersedia.</p>
                        </div>

                        <!-- Catatan Siswa -->
                        <div class="tab-pane fade" id="catatan" role="tabpanel">
                            <?php
                            // Get catatan siswa (simulasi data jika tabel belum ada)
                            $catatan_data = [
                                [
                                    'id' => 1,
                                    'tanggal' => '2025-07-05',
                                    'jenis' => 'Positif',
                                    'judul' => 'Prestasi Akademik',
                                    'isi_catatan' => 'Siswa menunjukkan peningkatan yang signifikan dalam mata pelajaran Matematika. Aktif bertanya dan mengerjakan tugas dengan baik.',
                                    'guru_pembuat' => 'Budi Santoso, S.Pd'
                                ],
                                [
                                    'id' => 2,
                                    'tanggal' => '2025-07-03',
                                    'jenis' => 'Peringatan',
                                    'judul' => 'Keterlambatan',
                                    'isi_catatan' => 'Siswa terlambat masuk kelas sebanyak 3 kali dalam minggu ini. Perlu perhatian khusus untuk kedisiplinan waktu.',
                                    'guru_pembuat' => 'Siti Aminah, S.Pd'
                                ]
                            ];

                            $total_catatan = count($catatan_data);
                            $positif = count(array_filter($catatan_data, fn($c) => $c['jenis'] == 'Positif'));
                            $peringatan = count(array_filter($catatan_data, fn($c) => $c['jenis'] == 'Peringatan'));
                            $negatif = count(array_filter($catatan_data, fn($c) => $c['jenis'] == 'Negatif'));
                            ?>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-sticky-note me-2"></i>Catatan Siswa</h6>
                                <button class="btn btn-primary btn-sm" onclick="tambahCatatan()">
                                    <i class="fas fa-plus me-1"></i>Tambah Catatan
                                </button>
                            </div>

                            <!-- Statistik Catatan -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #17a2b8;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #17a2b8, #138496);">
                                                    <i class="fas fa-star text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-info"><?= $total_catatan ?></h4>
                                            <small class="text-muted">Total Catatan</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #28a745;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745, #1e7e34);">
                                                    <i class="fas fa-thumbs-up text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-success"><?= $positif ?></h4>
                                            <small class="text-muted">Catatan Positif</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #ffc107;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107, #e0a800);">
                                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-warning"><?= $peringatan ?></h4>
                                            <small class="text-muted">Peringatan</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #dc3545;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc3545, #c82333);">
                                                    <i class="fas fa-times-circle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-danger"><?= $negatif ?></h4>
                                            <small class="text-muted">Catatan Negatif</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Daftar Catatan -->
                            <?php if (!empty($catatan_data)): ?>
                                <div class="row">
                                    <?php foreach ($catatan_data as $catatan): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <div>
                                                    <?php
                                                    $badge_class = '';
                                                    $icon = '';
                                                    switch($catatan['jenis']) {
                                                        case 'Positif':
                                                            $badge_class = 'bg-success';
                                                            $icon = 'fas fa-thumbs-up';
                                                            break;
                                                        case 'Peringatan':
                                                            $badge_class = 'bg-warning text-dark';
                                                            $icon = 'fas fa-exclamation-triangle';
                                                            break;
                                                        case 'Negatif':
                                                            $badge_class = 'bg-danger';
                                                            $icon = 'fas fa-times-circle';
                                                            break;
                                                        default:
                                                            $badge_class = 'bg-info';
                                                            $icon = 'fas fa-info-circle';
                                                    }
                                                    ?>
                                                    <span class="badge <?= $badge_class ?>">
                                                        <i class="<?= $icon ?> me-1"></i><?= $catatan['jenis'] ?>
                                                    </span>
                                                </div>
                                                <small class="text-muted"><?= date('d/m/Y', strtotime($catatan['tanggal'])) ?></small>
                                            </div>
                                            <div class="card-body">
                                                <h6 class="card-title"><?= htmlspecialchars($catatan['judul']) ?></h6>
                                                <p class="card-text"><?= htmlspecialchars($catatan['isi_catatan']) ?></p>
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>Oleh: <?= htmlspecialchars($catatan['guru_pembuat']) ?>
                                                </small>
                                            </div>
                                            <div class="card-footer">
                                                <div class="btn-group btn-group-sm w-100">
                                                    <button class="btn btn-outline-warning" onclick="editCatatan('<?= $catatan['id'] ?>')">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="hapusCatatan('<?= $catatan['id'] ?>')">
                                                        <i class="fas fa-trash"></i> Hapus
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-sticky-note fa-3x text-muted mb-3"></i>
                                    <h6>Belum ada catatan</h6>
                                    <p class="text-muted">Siswa ini belum memiliki catatan dari guru</p>
                                    <button class="btn btn-primary" onclick="tambahCatatan()">
                                        <i class="fas fa-plus me-1"></i>Tambah Catatan Pertama
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Berkas Siswa -->
                        <div class="tab-pane fade" id="berkas" role="tabpanel">
                            <?php
                            // Get berkas siswa (simulasi data jika tabel belum ada)
                            $berkas_data = [
                                [
                                    'id' => 1,
                                    'nama_berkas' => 'Kartu Keluarga',
                                    'jenis_berkas' => 'Dokumen Identitas',
                                    'nama_file' => 'kk_ahmad_rizki.pdf',
                                    'ukuran_file' => '2.5 MB',
                                    'tanggal_upload' => '2025-07-01',
                                    'status' => 'Terverifikasi',
                                    'keterangan' => 'Dokumen lengkap dan sesuai'
                                ],
                                [
                                    'id' => 2,
                                    'nama_berkas' => 'Akta Kelahiran',
                                    'jenis_berkas' => 'Dokumen Identitas',
                                    'nama_file' => 'akta_ahmad_rizki.pdf',
                                    'ukuran_file' => '1.8 MB',
                                    'tanggal_upload' => '2025-07-02',
                                    'status' => 'Pending',
                                    'keterangan' => 'Menunggu verifikasi'
                                ],
                                [
                                    'id' => 3,
                                    'nama_berkas' => 'Ijazah SD',
                                    'jenis_berkas' => 'Dokumen Akademik',
                                    'nama_file' => 'ijazah_sd_ahmad.pdf',
                                    'ukuran_file' => '3.2 MB',
                                    'tanggal_upload' => '2025-07-03',
                                    'status' => 'Terverifikasi',
                                    'keterangan' => 'Dokumen asli telah diverifikasi'
                                ]
                            ];

                            $total_berkas = count($berkas_data);
                            $terverifikasi = count(array_filter($berkas_data, fn($b) => $b['status'] == 'Terverifikasi'));
                            $pending = count(array_filter($berkas_data, fn($b) => $b['status'] == 'Pending'));
                            $ditolak = count(array_filter($berkas_data, fn($b) => $b['status'] == 'Ditolak'));
                            ?>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="fas fa-folder me-2"></i>Berkas Siswa</h6>
                                <button class="btn btn-primary btn-sm" onclick="uploadBerkas()">
                                    <i class="fas fa-upload me-1"></i>Upload Berkas
                                </button>
                            </div>

                            <!-- Statistik Berkas -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #6f42c1;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                                                    <i class="fas fa-folder text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-purple"><?= $total_berkas ?></h4>
                                            <small class="text-muted">Total Berkas</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #28a745;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745, #1e7e34);">
                                                    <i class="fas fa-check-circle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-success"><?= $terverifikasi ?></h4>
                                            <small class="text-muted">Terverifikasi</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #ffc107;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107, #e0a800);">
                                                    <i class="fas fa-clock text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-warning"><?= $pending ?></h4>
                                            <small class="text-muted">Pending</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center" style="border-left: 4px solid #dc3545;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-center mb-2">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc3545, #c82333);">
                                                    <i class="fas fa-times-circle text-white"></i>
                                                </div>
                                            </div>
                                            <h4 class="text-danger"><?= $ditolak ?></h4>
                                            <small class="text-muted">Ditolak</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Daftar Berkas -->
                            <?php if (!empty($berkas_data)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Nama Berkas</th>
                                                <th>Jenis</th>
                                                <th>File</th>
                                                <th>Ukuran</th>
                                                <th>Tanggal Upload</th>
                                                <th>Status</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($berkas_data as $berkas): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="me-2">
                                                            <?php
                                                            $file_ext = pathinfo($berkas['nama_file'], PATHINFO_EXTENSION);
                                                            $icon_class = '';
                                                            $icon_color = '';
                                                            switch(strtolower($file_ext)) {
                                                                case 'pdf':
                                                                    $icon_class = 'fas fa-file-pdf';
                                                                    $icon_color = 'text-danger';
                                                                    break;
                                                                case 'doc':
                                                                case 'docx':
                                                                    $icon_class = 'fas fa-file-word';
                                                                    $icon_color = 'text-primary';
                                                                    break;
                                                                case 'jpg':
                                                                case 'jpeg':
                                                                case 'png':
                                                                    $icon_class = 'fas fa-file-image';
                                                                    $icon_color = 'text-success';
                                                                    break;
                                                                default:
                                                                    $icon_class = 'fas fa-file';
                                                                    $icon_color = 'text-secondary';
                                                            }
                                                            ?>
                                                            <i class="<?= $icon_class ?> <?= $icon_color ?> fa-lg"></i>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?= htmlspecialchars($berkas['nama_berkas']) ?></div>
                                                            <small class="text-muted"><?= htmlspecialchars($berkas['keterangan']) ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?= htmlspecialchars($berkas['jenis_berkas']) ?></span>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?= htmlspecialchars($berkas['nama_file']) ?></small>
                                                </td>
                                                <td><?= $berkas['ukuran_file'] ?></td>
                                                <td><?= date('d/m/Y', strtotime($berkas['tanggal_upload'])) ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch($berkas['status']) {
                                                        case 'Terverifikasi':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'Pending':
                                                            $status_class = 'bg-warning text-dark';
                                                            break;
                                                        case 'Ditolak':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?= $status_class ?>">
                                                        <?= $berkas['status'] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" title="Lihat" onclick="lihatBerkas('<?= $berkas['id'] ?>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" title="Download" onclick="downloadBerkas('<?= $berkas['id'] ?>')">
                                                            <i class="fas fa-download"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" title="Hapus" onclick="hapusBerkas('<?= $berkas['id'] ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                    <h6>Belum ada berkas</h6>
                                    <p class="text-muted">Siswa ini belum mengupload berkas apapun</p>
                                    <button class="btn btn-primary" onclick="uploadBerkas()">
                                        <i class="fas fa-upload me-1"></i>Upload Berkas Pertama
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function tambahAbsensi() {
            Swal.fire({
                title: 'Tambah Absensi',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Siswa</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($siswa['nama_lengkap']) ?> (<?= $siswa['nisn'] ?>)" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="tanggalAbsensi" value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status Kehadiran</label>
                            <select class="form-select" id="statusAbsensi">
                                <option value="Hadir">Hadir</option>
                                <option value="Sakit">Sakit</option>
                                <option value="Izin">Izin</option>
                                <option value="Alpa">Alpa</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Jam Masuk</label>
                                <input type="time" class="form-control" id="jamMasuk" value="07:15">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Jam Keluar</label>
                                <input type="time" class="form-control" id="jamKeluar" value="15:30">
                            </div>
                        </div>
                        <div class="mb-3 mt-3">
                            <label class="form-label">Catatan</label>
                            <textarea class="form-control" id="catatanAbsensi" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '500px'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Redirect ke halaman tambah absensi dengan parameter
                    window.location.href = '../absensi/tambah.php?siswa_nisn=<?= $siswa['nisn'] ?>';
                }
            });
        }

        function editAbsensi(id) {
            window.location.href = '../absensi/edit.php?id=' + id;
        }

        function hapusAbsensi(id) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus data absensi ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '../../controllers/AbsensiController.php?hapus=' + id;
                }
            });
        }

        // Functions untuk Catatan Siswa
        function tambahCatatan() {
            Swal.fire({
                title: 'Tambah Catatan Siswa',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Siswa</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($siswa['nama_lengkap']) ?> (<?= $siswa['nisn'] ?>)" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jenis Catatan</label>
                            <select class="form-select" id="jenisCatatan">
                                <option value="Positif">Catatan Positif</option>
                                <option value="Peringatan">Peringatan</option>
                                <option value="Negatif">Catatan Negatif</option>
                                <option value="Umum">Catatan Umum</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Judul Catatan</label>
                            <input type="text" class="form-control" id="judulCatatan" placeholder="Masukkan judul catatan">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Isi Catatan</label>
                            <textarea class="form-control" id="isiCatatan" rows="4" placeholder="Tulis catatan detail di sini..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="tanggalCatatan" value="<?= date('Y-m-d') ?>">
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan Catatan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '600px'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Catatan siswa berhasil ditambahkan',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }

        function editCatatan(id) {
            Swal.fire({
                title: 'Edit Catatan',
                text: 'Fitur edit catatan akan segera tersedia',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        }

        function hapusCatatan(id) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus catatan ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Catatan berhasil dihapus',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }

        // Functions untuk Berkas Siswa
        function uploadBerkas() {
            Swal.fire({
                title: 'Upload Berkas Siswa',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Siswa</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($siswa['nama_lengkap']) ?> (<?= $siswa['nisn'] ?>)" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nama Berkas</label>
                            <input type="text" class="form-control" id="namaBerkas" placeholder="Contoh: Kartu Keluarga">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jenis Berkas</label>
                            <select class="form-select" id="jenisBerkas">
                                <option value="Dokumen Identitas">Dokumen Identitas</option>
                                <option value="Dokumen Akademik">Dokumen Akademik</option>
                                <option value="Dokumen Kesehatan">Dokumen Kesehatan</option>
                                <option value="Dokumen Lainnya">Dokumen Lainnya</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">File</label>
                            <input type="file" class="form-control" id="fileBerkas" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <small class="text-muted">Format yang didukung: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Keterangan</label>
                            <textarea class="form-control" id="keteranganBerkas" rows="2" placeholder="Keterangan tambahan (opsional)"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-upload me-1"></i>Upload Berkas',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#007bff',
                cancelButtonColor: '#6c757d',
                width: '600px'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Berkas berhasil diupload dan menunggu verifikasi',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }

        function lihatBerkas(id) {
            Swal.fire({
                title: 'Preview Berkas',
                text: 'Fitur preview berkas akan segera tersedia',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        }

        function downloadBerkas(id) {
            Swal.fire({
                title: 'Download Berkas',
                text: 'Berkas akan segera didownload',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            });
        }

        function hapusBerkas(id) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus berkas ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Berkas berhasil dihapus',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }
    </script>
</body>
</html>


