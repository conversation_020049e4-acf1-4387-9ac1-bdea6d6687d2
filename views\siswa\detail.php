<?php
session_start();
require_once '../../config/db.php';

$nisn = $_GET['nisn'] ?? '';
$query = "SELECT s.*, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.nisn = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Data Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            height: fit-content;
        }
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: #6c757d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }
        .profile-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .profile-id {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        .action-btn {
            width: 100%;
            margin-bottom: 8px;
            text-align: left;
        }
        .btn-edit { background: #ffc107; border-color: #ffc107; }
        .btn-upload { background: #20c997; border-color: #20c997; }
        .btn-notes { background: #17a2b8; border-color: #17a2b8; }
        .btn-back { background: #6c757d; border-color: #6c757d; }

        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background: none;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .data-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .data-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }
        .data-content {
            flex: 1;
        }
        .data-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 2px;
        }
        .data-value {
            font-weight: 500;
            color: #333;
        }
        .bg-blue { background: #007bff; }
        .bg-green { background: #28a745; }
        .bg-orange { background: #fd7e14; }
        .bg-purple { background: #6f42c1; }
        .bg-teal { background: #20c997; }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link active" href="index.php">Data Siswa</a>
                <a class="nav-link" href="../kelas/index.php">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h4 class="mb-4">Preview Data Siswa</h4>

        <div class="row">
            <!-- Profile Card -->
            <div class="col-md-3">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    <div class="profile-name"><?php echo htmlspecialchars($siswa['nama_lengkap']); ?></div>
                    <div class="profile-id">NIS: A25.285</div>
                    <div class="profile-id">Kelas: <?php echo $siswa['nama_kelas'] ?? 'Belum ada kelas'; ?></div>

                    <hr>

                    <button class="btn btn-edit text-white action-btn" onclick="window.location.href='edit.php?nisn=<?php echo $siswa['nisn']; ?>'">
                        <i class="fas fa-edit me-2"></i>Edit Data Siswa
                    </button>
                    <button class="btn btn-upload text-white action-btn">
                        <i class="fas fa-upload me-2"></i>Upload Berkas
                    </button>
                    <button class="btn btn-notes text-white action-btn">
                        <i class="fas fa-sticky-note me-2"></i>Tambah Catatan
                    </button>
                    <button class="btn btn-back text-white action-btn" onclick="window.location.href='index.php'">
                        <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar
                    </button>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-md-9">
                <div class="content-card">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs mb-4" id="siswaTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab">
                                <i class="fas fa-user me-2"></i>Informasi Personal
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absensi-tab" data-bs-toggle="tab" data-bs-target="#absensi" type="button" role="tab">
                                <i class="fas fa-calendar-check me-2"></i>Data Absensi
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="akademik-tab" data-bs-toggle="tab" data-bs-target="#akademik" type="button" role="tab">
                                <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="catatan-tab" data-bs-toggle="tab" data-bs-target="#catatan" type="button" role="tab">
                                <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="berkas-tab" data-bs-toggle="tab" data-bs-target="#berkas" type="button" role="tab">
                                <i class="fas fa-folder me-2"></i>Berkas Siswa
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="siswaTabContent">
                        <!-- Informasi Personal -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3"><i class="fas fa-id-card me-2"></i>Data Identitas</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-blue">
                                            <i class="fas fa-id-badge"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">NISN</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['nisn']); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-green">
                                            <i class="fas fa-id-card"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">NIK</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['nik'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-orange">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">No. KK</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['no_kk'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-purple">
                                            <i class="fas fa-tint"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Golongan Darah</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['golongan_darah'] ?? 'Tidak ada'); ?></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="mb-3"><i class="fas fa-map-marker-alt me-2"></i>Data Kelahiran</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-teal">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Tempat Lahir</div>
                                            <div class="data-value"><?php echo htmlspecialchars($siswa['tempat_lahir'] ?? 'Gunungkidul'); ?></div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-blue">
                                            <i class="fas fa-calendar"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Tanggal Lahir</div>
                                            <div class="data-value"><?php echo date('d F Y', strtotime($siswa['tanggal_lahir'] ?? '2007-08-08')); ?> (17 tahun)</div>
                                        </div>
                                    </div>

                                    <h6 class="mb-3 mt-4"><i class="fas fa-phone me-2"></i>Kontak</h6>

                                    <div class="data-row">
                                        <div class="data-icon bg-green">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">No. Telepon</div>
                                            <div class="data-value">Tidak ada</div>
                                        </div>
                                    </div>

                                    <div class="data-row">
                                        <div class="data-icon bg-orange">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="data-content">
                                            <div class="data-label">Email</div>
                                            <div class="data-value">Tidak ada</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Absensi -->
                        <div class="tab-pane fade" id="absensi" role="tabpanel">
                            <h6><i class="fas fa-calendar-check me-2"></i>Data Absensi</h6>
                            <p class="text-muted">Lihat data absensi lengkap siswa ini.</p>
                            <a href="absensi.php?nisn=<?php echo $siswa['nisn']; ?>" class="btn btn-primary">
                                <i class="fas fa-calendar-check me-2"></i>Lihat Data Absensi
                            </a>
                        </div>

                        <!-- Informasi Akademik -->
                        <div class="tab-pane fade" id="akademik" role="tabpanel">
                            <h6><i class="fas fa-graduation-cap me-2"></i>Informasi Akademik</h6>
                            <p class="text-muted">Data akademik akan segera tersedia.</p>
                        </div>

                        <!-- Catatan Siswa -->
                        <div class="tab-pane fade" id="catatan" role="tabpanel">
                            <h6><i class="fas fa-sticky-note me-2"></i>Catatan Siswa</h6>
                            <p class="text-muted">Belum ada catatan untuk siswa ini.</p>
                        </div>

                        <!-- Berkas Siswa -->
                        <div class="tab-pane fade" id="berkas" role="tabpanel">
                            <h6><i class="fas fa-folder me-2"></i>Berkas Siswa</h6>
                            <p class="text-muted">Belum ada berkas yang diupload.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>


