<?php
session_start();
require_once '../../config/db.php';

// Ambil data kelas
$query = "SELECT k.*, COUNT(s.nisn) as jumlah_siswa
          FROM kelas k
          LEFT JOIN siswa s ON k.id = s.kelas_id
          GROUP BY k.id
          ORDER BY k.nama_kelas";
$result = $conn->query($query);

// Hitung statistik
$total_kelas = $conn->query("SELECT COUNT(*) as total FROM kelas")->fetch_assoc()['total'];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Data Kelas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            font-size: 1.2rem;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Data Siswa</a>
                <a class="nav-link active" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-school me-2"></i>Daftar Kelas</h4>
                <small class="text-muted">2024/2025 (Tahun Berjalan)</small>
            </div>
            <div>
                <button class="btn btn-primary btn-sm" onclick="window.location.href='tambah.php'">
                    <i class="fas fa-plus"></i> Tambah Kelas
                </button>
            </div>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th>ID</th>
                            <th>Nama Kelas</th>
                            <th>Tingkat</th>
                            <th>Kurikulum</th>
                            <th>Tahun Pelajaran</th>
                            <th>Wali Kelas</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $row['id']; ?></td>
                            <td><strong><?php echo htmlspecialchars($row['nama_kelas']); ?></strong></td>
                            <td>
                                <span class="badge bg-primary">
                                    <?php echo htmlspecialchars($row['tingkat'] ?? 'KPA Pemrograman Web'); ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo htmlspecialchars($row['kurikulum'] ?? 'Kurikulum Merdeka'); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($row['tahun_pelajaran'] ?? '2024/2025'); ?></td>
                            <td><?php echo htmlspecialchars($row['wali_kelas'] ?? 'Y. Guntur Cahyo Dewantoro, ST.'); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="detail.php?id=<?php echo $row['id']; ?>" class="btn btn-outline-primary btn-sm" title="Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-outline-warning btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="../../controllers/KelasController.php?hapus=<?php echo $row['id']; ?>"
                                       class="btn btn-outline-danger btn-sm" title="Hapus"
                                       onclick="return confirm('Yakin ingin menghapus kelas ini?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>