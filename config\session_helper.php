<?php
/**
 * Session Helper untuk Aplikasi Siswa
 * Menangani session management dengan aman
 */

// Mulai session jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Inisialisasi session default untuk demo
 */
function initDefaultSession() {
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'instructor';
        $_SESSION['role'] = 'admin';
        $_SESSION['email'] = '<EMAIL>';
        $_SESSION['login_time'] = date('Y-m-d H:i:s');
    }
    
    // Pastikan role selalu tersedia
    if (!isset($_SESSION['role'])) {
        $_SESSION['role'] = 'admin';
    }
    
    // Pastikan username selalu tersedia
    if (!isset($_SESSION['username'])) {
        $_SESSION['username'] = 'instructor';
    }
}

/**
 * Cek apakah user sudah login
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Dapatkan role user dengan fallback
 */
function getUserRole() {
    return $_SESSION['role'] ?? 'admin';
}

/**
 * Dapatkan username dengan fallback
 */
function getUsername() {
    return $_SESSION['username'] ?? 'instructor';
}

/**
 * Dapatkan user ID dengan fallback
 */
function getUserId() {
    return $_SESSION['user_id'] ?? 1;
}

/**
 * Redirect ke login jika belum login (untuk halaman yang memerlukan auth)
 */
function requireLogin($redirect_to_login = true) {
    if (!isLoggedIn()) {
        if ($redirect_to_login) {
            // Untuk demo, set session default daripada redirect
            initDefaultSession();
        } else {
            header("Location: ../auth/login.php");
            exit;
        }
    }
}

/**
 * Logout user
 */
function logoutUser() {
    session_destroy();
    header("Location: ../auth/login.php");
    exit;
}

/**
 * Set session untuk user tertentu
 */
function setUserSession($user_id, $username, $role, $email = null) {
    $_SESSION['user_id'] = $user_id;
    $_SESSION['username'] = $username;
    $_SESSION['role'] = $role;
    $_SESSION['email'] = $email;
    $_SESSION['login_time'] = date('Y-m-d H:i:s');
}

/**
 * Dapatkan informasi session lengkap
 */
function getSessionInfo() {
    return [
        'user_id' => getUserId(),
        'username' => getUsername(),
        'role' => getUserRole(),
        'email' => $_SESSION['email'] ?? null,
        'login_time' => $_SESSION['login_time'] ?? null,
        'is_logged_in' => isLoggedIn()
    ];
}

/**
 * Cek apakah user memiliki role tertentu
 */
function hasRole($required_role) {
    $user_role = getUserRole();
    
    // Admin memiliki akses ke semua
    if ($user_role === 'admin') {
        return true;
    }
    
    return $user_role === $required_role;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validasi CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Auto-initialize session untuk demo
initDefaultSession();
?>
