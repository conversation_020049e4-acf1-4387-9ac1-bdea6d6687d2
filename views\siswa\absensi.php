<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Set session untuk demo jika belum ada
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

$nisn = $_GET['nisn'] ?? '';

// Get data siswa
$siswa_query = "SELECT s.*, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.nisn = ?";
$siswa_stmt = $conn->prepare($siswa_query);
$siswa_stmt->bind_param("s", $nisn);
$siswa_stmt->execute();
$siswa = $siswa_stmt->get_result()->fetch_assoc();

if (!$siswa) {
    header('Location: index.php');
    exit;
}

// Get statistik absensi bulan ini
$bulan_ini = date('n');
$tahun_ini = date('Y');

$stats_query = "
    SELECT
        COUNT(*) as total_hari,
        SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
        SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
        SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as total_izin,
        SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as total_alpa
    FROM absensi
    WHERE siswa_nisn = ? AND MONTH(tanggal) = ? AND YEAR(tanggal) = ?
";

$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("sii", $nisn, $bulan_ini, $tahun_ini);
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// Hitung persentase kehadiran
$total_hari = $stats['total_hari'] ?: 1;
$persentase_kehadiran = round(($stats['total_hadir'] / $total_hari) * 100, 1);

// Get riwayat absensi terbaru
$riwayat_query = "
    SELECT * FROM absensi
    WHERE siswa_nisn = ?
    ORDER BY tanggal DESC
    LIMIT 10
";

$riwayat_stmt = $conn->prepare($riwayat_query);
$riwayat_stmt->bind_param("s", $nisn);
$riwayat_stmt->execute();
$riwayat_absensi = $riwayat_stmt->get_result();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Absensi - <?= htmlspecialchars($siswa['nama_lengkap']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .tab-nav {
            background: white;
            border-radius: 10px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-nav .nav-link {
            border-radius: 8px;
            color: #6c757d;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            margin: 0 0.25rem;
            transition: all 0.3s;
        }
        
        .tab-nav .nav-link.active {
            background: #007bff;
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            margin-bottom: 1rem;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        
        .stats-kehadiran { background: linear-gradient(135deg, #4CAF50, #45a049); }
        .stats-sakit { background: linear-gradient(135deg, #f44336, #d32f2f); }
        .stats-izin { background: linear-gradient(135deg, #ff9800, #f57c00); }
        .stats-alpa { background: linear-gradient(135deg, #9e9e9e, #757575); }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .riwayat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .badge-hadir { background-color: #28a745; }
        .badge-sakit { background-color: #dc3545; }
        .badge-izin { background-color: #ffc107; color: #000; }
        .badge-alpa { background-color: #6c757d; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="index.php">Data Siswa</a>
                <a class="nav-link" href="../kelas/index.php">Data Kelas</a>
                <a class="nav-link" href="../absensi/index.php">Data Absensi</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="profile-avatar">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <h3 class="mb-1"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h3>
                        <p class="mb-1">NISN: <?= htmlspecialchars($siswa['nisn']) ?></p>
                        <p class="mb-0">Kelas: <?= htmlspecialchars($siswa['nama_kelas'] ?? 'Belum ada kelas') ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-nav">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link" href="detail.php?nisn=<?= $nisn ?>">
                        <i class="fas fa-user me-2"></i>Informasi Personal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="absensi.php?nisn=<?= $nisn ?>">
                        <i class="fas fa-calendar-check me-2"></i>Data Absensi
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-graduation-cap me-2"></i>Informasi Akademik
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-sticky-note me-2"></i>Catatan Siswa
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="alert('Fitur akan segera tersedia!')">
                        <i class="fas fa-folder me-2"></i>Berkas Siswa
                    </a>
                </li>
            </ul>
        </div>

        <!-- Statistik Absensi -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-kehadiran text-white">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number"><?= $persentase_kehadiran ?>%</div>
                    <div class="stats-label">Kehadiran</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-sakit text-white">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <div class="stats-number"><?= $stats['total_sakit'] ?></div>
                    <div class="stats-label">Sakit</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-izin text-white">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <div class="stats-number"><?= $stats['total_izin'] ?></div>
                    <div class="stats-label">Ijin</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon stats-alpa text-white">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stats-number"><?= $stats['total_alpa'] ?></div>
                    <div class="stats-label">Alpha</div>
                </div>
            </div>
        </div>

        <!-- Riwayat Absensi -->
        <div class="riwayat-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Absensi</h5>
                <button class="btn btn-primary btn-sm" onclick="tambahAbsensi()">
                    <i class="fas fa-plus me-1"></i>Tambah Absensi
                </button>
            </div>

            <?php if ($riwayat_absensi->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Jam Masuk</th>
                                <th>Jam Keluar</th>
                                <th>Catatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $riwayat_absensi->fetch_assoc()): ?>
                            <tr>
                                <td><?= date('d/m/Y', strtotime($row['tanggal'])) ?></td>
                                <td>
                                    <span class="badge badge-<?= strtolower($row['keterangan']) ?>">
                                        <?= $row['keterangan'] ?>
                                    </span>
                                </td>
                                <td><?= $row['jam_masuk'] ? date('H:i', strtotime($row['jam_masuk'])) : '-' ?></td>
                                <td><?= $row['jam_keluar'] ? date('H:i', strtotime($row['jam_keluar'])) : '-' ?></td>
                                <td><?= htmlspecialchars($row['catatan'] ?? '-') ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" title="Edit" onclick="editAbsensi('<?= $row['id'] ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Hapus" onclick="hapusAbsensi('<?= $row['id'] ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-calendar-times"></i>
                    <h5>Belum ada catatan absensi</h5>
                    <p>Siswa ini belum memiliki catatan ketidakhadiran</p>
                    <button class="btn btn-primary" onclick="tambahAbsensi()">
                        <i class="fas fa-plus me-1"></i>Tambah Absensi Pertama
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function tambahAbsensi() {
            window.location.href = '../absensi/tambah.php?siswa_nisn=<?= $nisn ?>';
        }

        function editAbsensi(id) {
            window.location.href = '../absensi/edit.php?id=' + id;
        }

        function hapusAbsensi(id) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus data absensi ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '../../controllers/AbsensiController.php?hapus=' + id;
                }
            });
        }
    </script>
</body>
</html>
