<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit;
}

require_once '../../config/db.php';

// Get data siswa untuk dropdown
$siswa_result = $conn->query("SELECT s.nisn, s.nama_lengkap, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id ORDER BY s.nama_lengkap");

// Get jenis catatan options
$jenis_result = $conn->query("SHOW COLUMNS FROM catatan_siswa LIKE 'jenis_catatan'");
$jenis_row = $jenis_result->fetch_assoc();
$enum_string = $jenis_row['Type'];
preg_match_all("/'([^']+)'/", $enum_string, $matches);
$jenis_options = $matches[1];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah Catatan Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        .required {
            color: #dc3545;
        }
        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link active" href="index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-plus-circle text-primary me-2"></i>Tambah Catatan Siswa</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/index.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Catatan Siswa</a></li>
                        <li class="breadcrumb-item active">Tambah Catatan</li>
                    </ol>
                </nav>
            </div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Kembali
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>Terjadi kesalahan saat menyimpan catatan!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Form Tambah Catatan -->
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Form Tambah Catatan</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="../../controllers/CatatanController.php" id="formTambahCatatan">
                    <!-- Informasi Siswa -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-user me-2"></i>Informasi Siswa</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Pilih Siswa <span class="required">*</span></label>
                                <select class="form-select" name="siswa_nisn" required id="selectSiswa">
                                    <option value="">-- Pilih Siswa --</option>
                                    <?php while ($siswa = $siswa_result->fetch_assoc()): ?>
                                        <option value="<?= $siswa['nisn'] ?>">
                                            <?= htmlspecialchars($siswa['nama_lengkap']) ?> 
                                            <?= $siswa['nama_kelas'] ? '(' . $siswa['nama_kelas'] . ')' : '' ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Tanggal <span class="required">*</span></label>
                                <input type="date" class="form-control" name="tanggal" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <!-- Detail Catatan -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-clipboard-list me-2"></i>Detail Catatan</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Jenis Catatan <span class="required">*</span></label>
                                <select class="form-select" name="jenis_catatan" required>
                                    <option value="">-- Pilih Jenis Catatan --</option>
                                    <?php foreach ($jenis_options as $jenis): ?>
                                        <option value="<?= $jenis ?>"><?= $jenis ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Judul Catatan <span class="required">*</span></label>
                                <input type="text" class="form-control" name="judul_catatan" required 
                                       placeholder="Masukkan judul catatan..." maxlength="255" id="judulCatatan">
                                <div class="char-counter mt-1">
                                    <span id="judulCounter">0</span>/255 karakter
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">Isi Catatan <span class="required">*</span></label>
                                <textarea class="form-control" name="isi_catatan" rows="5" required 
                                          placeholder="Tulis catatan lengkap di sini..." id="isiCatatan"></textarea>
                                <div class="char-counter mt-1">
                                    <span id="isiCounter">0</span> karakter
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Prioritas dan Status -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-cog me-2"></i>Prioritas dan Status</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Tingkat Prioritas <span class="required">*</span></label>
                                <select class="form-select" name="tingkat_prioritas" required>
                                    <option value="Sedang" selected>Sedang</option>
                                    <option value="Rendah">Rendah</option>
                                    <option value="Tinggi">Tinggi</option>
                                    <option value="Sangat Tinggi">Sangat Tinggi</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Status <span class="required">*</span></label>
                                <select class="form-select" name="status" required>
                                    <option value="Aktif" selected>Aktif</option>
                                    <option value="Selesai">Selesai</option>
                                    <option value="Ditunda">Ditunda</option>
                                    <option value="Dibatalkan">Dibatalkan</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Tindak Lanjut -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-tasks me-2"></i>Tindak Lanjut (Opsional)</h6>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Rencana Tindak Lanjut atau Rekomendasi</label>
                                <textarea class="form-control" name="tindak_lanjut" rows="3" 
                                          placeholder="Rencana tindak lanjut atau rekomendasi..." id="tindakLanjut"></textarea>
                                <div class="char-counter mt-1">
                                    <span id="tindakCounter">0</span> karakter
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Tanggal Tindak Lanjut (Opsional)</label>
                                <input type="date" class="form-control" name="tanggal_tindak_lanjut" 
                                       min="<?= date('Y-m-d') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Informasi Tambahan -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Informasi Tambahan</h6>
                        <ul class="mb-0">
                            <li><strong>Sakit:</strong> Tidak hadir karena sakit dengan surat keterangan</li>
                            <li><strong>Izin:</strong> Tidak hadir dengan izin yang sah</li>
                            <li><strong>Alpha:</strong> Tidak hadir tanpa keterangan</li>
                        </ul>
                    </div>

                    <!-- Tombol Submit -->
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="fas fa-times me-1"></i>Batal
                        </button>
                        <button type="submit" name="tambah_catatan" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Simpan Catatan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Character counters
        document.getElementById('judulCatatan').addEventListener('input', function() {
            document.getElementById('judulCounter').textContent = this.value.length;
        });

        document.getElementById('isiCatatan').addEventListener('input', function() {
            document.getElementById('isiCounter').textContent = this.value.length;
        });

        document.getElementById('tindakLanjut').addEventListener('input', function() {
            document.getElementById('tindakCounter').textContent = this.value.length;
        });

        // Form validation
        document.getElementById('formTambahCatatan').addEventListener('submit', function(e) {
            const siswa = document.querySelector('[name="siswa_nisn"]').value;
            const jenis = document.querySelector('[name="jenis_catatan"]').value;
            const judul = document.querySelector('[name="judul_catatan"]').value;
            const isi = document.querySelector('[name="isi_catatan"]').value;

            if (!siswa || !jenis || !judul.trim() || !isi.trim()) {
                e.preventDefault();
                alert('Mohon lengkapi semua field yang wajib diisi!');
                return false;
            }

            if (judul.length > 255) {
                e.preventDefault();
                alert('Judul catatan maksimal 255 karakter!');
                return false;
            }
        });
    </script>
</body>
</html>
