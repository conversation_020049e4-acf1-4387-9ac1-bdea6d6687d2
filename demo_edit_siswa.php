<?php
require_once 'config/session_helper.php';
require_once 'config/db.php';

// Get sample student data
$test_nisn = '0076116641';
$stmt = $conn->prepare("SELECT s.*, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.nisn = ?");
$stmt->bind_param("s", $test_nisn);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    echo "Student not found!";
    exit;
}

// Get kelas list
$kelas_result = $conn->query("SELECT * FROM kelas ORDER BY nama_kelas");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Edit Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .form-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Demo Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-edit me-2"></i>Demo Edit Siswa</h2>
                    <p class="mb-0">Test fungsi edit data siswa dengan form yang sudah diperbaiki</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="views/siswa/detail.php?nisn=<?= $siswa['nisn'] ?>" class="btn btn-light">
                        <i class="fas fa-eye me-1"></i>Lihat Detail
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Status Info -->
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Informasi Test</h5>
            <p class="mb-2"><strong>Student:</strong> <?= htmlspecialchars($siswa['nama_lengkap']) ?></p>
            <p class="mb-2"><strong>NISN:</strong> <?= $siswa['nisn'] ?></p>
            <p class="mb-0"><strong>Current Email:</strong> <?= $siswa['email'] ?? 'Not set' ?></p>
        </div>

        <!-- Edit Form -->
        <div class="form-container">
            <h4 class="section-title">
                <i class="fas fa-user-edit me-2"></i>Edit Data Siswa
            </h4>

            <form action="controllers/SiswaController.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="edit_siswa" value="1">
                <input type="hidden" name="nisn" value="<?= $siswa['nisn'] ?>">

                <!-- Basic Info -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">NISN</label>
                            <input type="text" class="form-control" value="<?= $siswa['nisn'] ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Nama Lengkap *</label>
                            <input type="text" class="form-control" name="nama_lengkap" 
                                   value="<?= htmlspecialchars($siswa['nama_lengkap']) ?>" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email" 
                                   value="<?= htmlspecialchars($siswa['email'] ?? '') ?>"
                                   placeholder="Masukkan email baru untuk test">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">No. Telepon</label>
                            <input type="text" class="form-control" name="no_telepon" 
                                   value="<?= htmlspecialchars($siswa['no_telepon'] ?? '') ?>">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Tempat Lahir</label>
                            <input type="text" class="form-control" name="tempat_lahir" 
                                   value="<?= htmlspecialchars($siswa['tempat_lahir'] ?? '') ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Tanggal Lahir</label>
                            <input type="date" class="form-control" name="tanggal_lahir" 
                                   value="<?= $siswa['tanggal_lahir'] ?>">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Jenis Kelamin</label>
                            <select class="form-select" name="jenis_kelamin">
                                <option value="">Pilih Jenis Kelamin</option>
                                <option value="Laki-laki" <?= $siswa['jenis_kelamin'] == 'Laki-laki' ? 'selected' : '' ?>>Laki-laki</option>
                                <option value="Perempuan" <?= $siswa['jenis_kelamin'] == 'Perempuan' ? 'selected' : '' ?>>Perempuan</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Kelas</label>
                            <select class="form-select" name="kelas_id">
                                <option value="">Pilih Kelas</option>
                                <?php while ($kelas = $kelas_result->fetch_assoc()): ?>
                                    <option value="<?= $kelas['id'] ?>" 
                                            <?= $siswa['kelas_id'] == $kelas['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($kelas['nama_kelas']) ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Alamat</label>
                    <textarea class="form-control" name="alamat" rows="3"><?= htmlspecialchars($siswa['alamat'] ?? '') ?></textarea>
                </div>

                <!-- Hidden fields for other data -->
                <input type="hidden" name="nik" value="<?= $siswa['nik'] ?>">
                <input type="hidden" name="no_kk" value="<?= $siswa['no_kk'] ?>">
                <input type="hidden" name="golongan_darah" value="<?= $siswa['golongan_darah'] ?>">
                <input type="hidden" name="agama" value="<?= $siswa['agama'] ?>">
                <input type="hidden" name="nama_ayah" value="<?= $siswa['nama_ayah'] ?>">
                <input type="hidden" name="nama_ibu" value="<?= $siswa['nama_ibu'] ?>">
                <input type="hidden" name="pekerjaan_ayah" value="<?= $siswa['pekerjaan_ayah'] ?>">
                <input type="hidden" name="pekerjaan_ibu" value="<?= $siswa['pekerjaan_ibu'] ?>">
                <input type="hidden" name="no_telepon_ortu" value="<?= $siswa['no_telepon_ortu'] ?>">
                <input type="hidden" name="status" value="<?= $siswa['status'] ?>">

                <!-- Submit Buttons -->
                <hr>
                <div class="d-flex justify-content-end gap-2">
                    <a href="views/siswa/detail.php?nisn=<?= $siswa['nisn'] ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>

        <!-- Test Instructions -->
        <div class="alert alert-warning mt-4">
            <h5><i class="fas fa-lightbulb me-2"></i>Cara Test</h5>
            <ol class="mb-0">
                <li>Ubah email atau data lainnya</li>
                <li>Klik "Simpan Perubahan"</li>
                <li>Akan redirect ke halaman detail dengan pesan sukses</li>
                <li>Verifikasi perubahan tersimpan</li>
            </ol>
        </div>

        <!-- Quick Links -->
        <div class="text-center mt-4">
            <a href="test_edit_siswa.php" class="btn btn-info me-2">
                <i class="fas fa-flask me-1"></i>Test Report
            </a>
            <a href="views/siswa/index.php" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i>Daftar Siswa
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
