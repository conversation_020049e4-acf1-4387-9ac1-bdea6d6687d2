<?php
require_once 'config/db.php';
require_once 'config/functions.php';

echo "<h2>🔧 Reset Admin Password</h2>";

// Generate new password hash
$new_password = 'admin123';
$password_hash = hashPassword($new_password);

echo "<p><strong>New password:</strong> $new_password</p>";
echo "<p><strong>New hash:</strong> " . substr($password_hash, 0, 30) . "...</p>";

// Update admin password
$stmt = $conn->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
$stmt->bind_param("s", $password_hash);

if ($stmt->execute()) {
    echo "<div style='color: green; font-weight: bold;'>✅ Password admin berhasil direset!</div>";
    
    // Test the new password
    echo "<h3>🧪 Testing New Password</h3>";
    $test_result = $conn->query("SELECT * FROM users WHERE username = 'admin'");
    if ($test_result->num_rows > 0) {
        $user = $test_result->fetch_assoc();
        $verify_result = verifyPassword($new_password, $user['password']);
        echo "<p>Password verification test: " . ($verify_result ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 Login Credentials:</h3>";
    echo "<p><strong>Username:</strong> admin</p>";
    echo "<p><strong>Password:</strong> admin123</p>";
    echo "<p><a href='views/auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to Login Page</a></p>";
    
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ Gagal mereset password!</div>";
    echo "<p>Error: " . $conn->error . "</p>";
}
?>
