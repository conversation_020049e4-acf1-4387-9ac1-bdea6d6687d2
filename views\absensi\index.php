<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login
requireLogin(true);

require_once '../../config/db.php';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$filter_keterangan = $_GET['filter_keterangan'] ?? '';
$filter_tanggal = $_GET['filter_tanggal'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(s.nama_lengkap LIKE ? OR s.nisn LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= 'ss';
}

if (!empty($filter_keterangan)) {
    $where_conditions[] = "a.keterangan = ?";
    $params[] = $filter_keterangan;
    $types .= 's';
}

if (!empty($filter_tanggal)) {
    $where_conditions[] = "DATE(a.tanggal) = ?";
    $params[] = $filter_tanggal;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total records
$count_sql = "
    SELECT COUNT(*) as total
    FROM absensi a
    JOIN siswa s ON a.siswa_nisn = s.nisn
    $where_clause
";

if (!empty($params)) {
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
} else {
    $total_records = $conn->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $limit);

// Get absensi data
$sql = "
    SELECT a.*, s.nama_lengkap, s.kelas_id, k.nama_kelas
    FROM absensi a
    JOIN siswa s ON a.siswa_nisn = s.nisn
    LEFT JOIN kelas k ON s.kelas_id = k.id
    $where_clause
    ORDER BY a.tanggal DESC, s.nama_lengkap ASC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;
$types .= 'ii';

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$absensi_list = $stmt->get_result();

// Get statistics
$stats = $conn->query("
    SELECT
        COUNT(*) as total_absensi,
        SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
        SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
        SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as total_izin,
        SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as total_alpa
    FROM absensi
    WHERE DATE(tanggal) = CURDATE()
")->fetch_assoc();

$persentase_kehadiran = $stats['total_absensi'] > 0 ? round(($stats['total_hadir'] / $stats['total_absensi']) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Absensi Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-card {
            border-left: 4px solid;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .stats-card.hadir { border-left-color: #28a745; }
        .stats-card.sakit { border-left-color: #dc3545; }
        .stats-card.izin { border-left-color: #ffc107; }
        .stats-card.alpa { border-left-color: #6c757d; }

        .badge-hadir { background-color: #28a745; }
        .badge-sakit { background-color: #dc3545; }
        .badge-izin { background-color: #ffc107; color: #000; }
        .badge-alpa { background-color: #6c757d; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link active" href="index.php">Absensi</a>
                <a class="nav-link" href="../berkas/index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-calendar-check text-primary me-2"></i>Absensi Siswa</h2>
            <a href="tambah.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Tambah Absensi
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>Absensi berhasil ditambahkan!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['updated'])): ?>
            <div class="alert alert-info alert-dismissible fade show">
                <i class="fas fa-edit me-2"></i>Absensi berhasil diupdate!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['deleted'])): ?>
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-trash me-2"></i>Absensi berhasil dihapus!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card hadir">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Hadir Hari Ini</h5>
                                <h2 class="text-success"><?= $stats['total_hadir'] ?></h2>
                                <small class="text-muted"><?= $persentase_kehadiran ?>% kehadiran</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card sakit">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Sakit</h5>
                                <h2 class="text-danger"><?= $stats['total_sakit'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-thermometer-half fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card izin">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Izin</h5>
                                <h2 class="text-warning"><?= $stats['total_izin'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-hand-paper fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card alpa">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-muted">Alpha</h5>
                                <h2 class="text-secondary"><?= $stats['total_alpa'] ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x text-secondary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter dan Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Cari Siswa</label>
                        <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Cari nama atau NISN siswa...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Keterangan</label>
                        <select class="form-select" name="filter_keterangan">
                            <option value="">Semua Keterangan</option>
                            <option value="Hadir" <?= $filter_keterangan == 'Hadir' ? 'selected' : '' ?>>Hadir</option>
                            <option value="Sakit" <?= $filter_keterangan == 'Sakit' ? 'selected' : '' ?>>Sakit</option>
                            <option value="Izin" <?= $filter_keterangan == 'Izin' ? 'selected' : '' ?>>Izin</option>
                            <option value="Alpa" <?= $filter_keterangan == 'Alpa' ? 'selected' : '' ?>>Alpha</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Tanggal</label>
                        <input type="date" class="form-control" name="filter_tanggal" value="<?= htmlspecialchars($filter_tanggal) ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabel Absensi -->
        <div class="card">
            <div class="card-body">
                <?php if ($absensi_list->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Tanggal</th>
                                <th>Siswa</th>
                                <th>Kelas</th>
                                <th>Keterangan</th>
                                <th>Jam</th>
                                <th>Catatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = ($page - 1) * $limit + 1;
                            while ($row = $absensi_list->fetch_assoc()):
                            ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= date('d/m/Y', strtotime($row['tanggal'])) ?></td>
                                <td>
                                    <strong><?= htmlspecialchars($row['nama_lengkap']) ?></strong><br>
                                    <small class="text-muted">NISN: <?= $row['siswa_nisn'] ?></small>
                                </td>
                                <td><?= $row['nama_kelas'] ?? 'Tidak ada kelas' ?></td>
                                <td>
                                    <span class="badge badge-<?= strtolower($row['keterangan']) ?>">
                                        <?= $row['keterangan'] ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($row['jam_masuk'] || $row['jam_keluar']): ?>
                                        <?= $row['jam_masuk'] ? date('H:i', strtotime($row['jam_masuk'])) : '-' ?> -
                                        <?= $row['jam_keluar'] ? date('H:i', strtotime($row['jam_keluar'])) : '-' ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($row['catatan']): ?>
                                        <?= substr(htmlspecialchars($row['catatan']), 0, 30) ?><?= strlen($row['catatan']) > 30 ? '...' : '' ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="edit.php?id=<?= $row['id'] ?>" class="btn btn-warning btn-sm" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="../../controllers/AbsensiController.php?hapus=<?= $row['id'] ?>"
                                           class="btn btn-danger btn-sm" title="Hapus"
                                           onclick="return confirm('Yakin ingin menghapus data absensi ini?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page-1 ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>">Previous</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page+1 ?>&search=<?= urlencode($search) ?>&filter_keterangan=<?= urlencode($filter_keterangan) ?>&filter_tanggal=<?= urlencode($filter_tanggal) ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>

                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada data absensi</h5>
                    <p class="text-muted">Belum ada data absensi yang tercatat. Klik tombol "Tambah Absensi" untuk menambahkan data pertama.</p>
                    <a href="tambah.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Tambah Absensi Pertama
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>