<?php
// Test file untuk KelasController
require_once 'config/db.php';

echo "<h2>Test KelasController</h2>";

// Test 1: Cek struktur tabel kelas
echo "<h3>1. Struktur Tabel Kelas</h3>";
$result = $conn->query("DESCRIBE kelas");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . $conn->error;
}

// Test 2: Test query UPDATE
echo "<h3>2. Test Query UPDATE</h3>";
$test_query = "UPDATE kelas SET nama_kelas = ?, tingkat = ?, kurikulum = ?, tahun_pelajaran = ?, kapasitas = ?, wali_kelas = ? WHERE id = ?";
$stmt = $conn->prepare($test_query);

if ($stmt) {
    echo "✅ Query UPDATE berhasil di-prepare<br>";
    echo "<strong>Query:</strong> " . htmlspecialchars($test_query) . "<br>";
    $stmt->close();
} else {
    echo "❌ Error preparing UPDATE query: " . $conn->error . "<br>";
}

// Test 3: Test query INSERT
echo "<h3>3. Test Query INSERT</h3>";
$test_insert_query = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum, tahun_pelajaran, kapasitas, wali_kelas) VALUES (?, ?, ?, ?, ?, ?)";
$stmt2 = $conn->prepare($test_insert_query);

if ($stmt2) {
    echo "✅ Query INSERT berhasil di-prepare<br>";
    echo "<strong>Query:</strong> " . htmlspecialchars($test_insert_query) . "<br>";
    $stmt2->close();
} else {
    echo "❌ Error preparing INSERT query: " . $conn->error . "<br>";
}

// Test 4: Cek data kelas yang ada
echo "<h3>4. Data Kelas yang Ada</h3>";
$kelas_result = $conn->query("SELECT * FROM kelas ORDER BY id");
if ($kelas_result && $kelas_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum</th><th>Tahun Pelajaran</th><th>Kapasitas</th><th>Wali Kelas</th></tr>";
    while ($row = $kelas_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_kelas']) . "</td>";
        echo "<td>" . htmlspecialchars($row['tingkat'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['kurikulum'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['tahun_pelajaran'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['kapasitas'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['wali_kelas'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Tidak ada data kelas atau error: " . $conn->error;
}

// Test 5: Test link edit untuk kelas pertama
echo "<h3>5. Test Link Edit</h3>";
$first_kelas = $conn->query("SELECT id, nama_kelas FROM kelas LIMIT 1");
if ($first_kelas && $first_kelas->num_rows > 0) {
    $kelas = $first_kelas->fetch_assoc();
    $id = $kelas['id'];
    $nama = $kelas['nama_kelas'];
    
    echo "<p><strong>Kelas untuk test:</strong> $nama (ID: $id)</p>";
    echo "<p><a href='views/kelas/edit.php?id=$id' target='_blank'>🔗 Test Edit Kelas</a></p>";
    echo "<p><a href='views/kelas/detail.php?id=$id' target='_blank'>🔗 Test Detail Kelas</a></p>";
} else {
    echo "Tidak ada kelas untuk ditest";
}

// Test 6: Cek file controller
echo "<h3>6. File Controller</h3>";
$controller_path = 'controllers/KelasController.php';
if (file_exists($controller_path)) {
    echo "✅ File KelasController.php ada<br>";
    echo "<strong>Path:</strong> " . realpath($controller_path) . "<br>";
    echo "<strong>Size:</strong> " . filesize($controller_path) . " bytes<br>";
    echo "<strong>Last Modified:</strong> " . date('Y-m-d H:i:s', filemtime($controller_path)) . "<br>";
} else {
    echo "❌ File KelasController.php tidak ditemukan<br>";
}

echo "<hr>";
echo "<p><a href='views/kelas/index.php'>🏠 Kembali ke Data Kelas</a></p>";
echo "<p><a href='demo_kelas.php'>🎭 Lihat Demo Kelas</a></p>";

$conn->close();
?>
