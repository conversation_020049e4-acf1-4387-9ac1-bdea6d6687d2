<?php
require_once 'config/db.php';

echo "=== MEMBUAT TABEL KELAS ===\n";

// Cek apakah tabel kelas sudah ada
$check_table = $conn->query("SHOW TABLES LIKE 'kelas'");
if ($check_table->num_rows == 0) {
    echo "Membuat tabel kelas...\n";
    $sql_kelas = "
    CREATE TABLE kelas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nama_kelas VARCHAR(50) NOT NULL,
        deskripsi TEXT,
        wali_kelas VARCHAR(100),
        tahun_ajaran VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql_kelas)) {
        echo "✓ Tabel kelas berhasil dibuat\n";
    } else {
        echo "✗ Error: " . $conn->error . "\n";
    }
} else {
    echo "Tabel kelas sudah ada\n";
}

$conn->close();
echo "\n🎉 Setup tabel kelas selesai!\n";
?>
