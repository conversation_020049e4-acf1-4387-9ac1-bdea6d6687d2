<?php
require_once '../config/db.php';
require_once '../config/functions.php';
session_start();

// Upload Berkas
if (isset($_POST['upload_berkas'])) {
    $siswa_nisn = $_POST['siswa_nisn'];
    $jenis_berkas = $_POST['jenis_berkas'];
    $keterangan = $_POST['keterangan'] ?? null;
    $uploaded_by = $_SESSION['user_id'];

    // Validasi file
    if (!isset($_FILES['file_berkas']) || $_FILES['file_berkas']['error'] !== UPLOAD_ERR_OK) {
        header("Location: ../views/berkas/upload.php?error=upload_failed");
        exit;
    }

    $file = $_FILES['file_berkas'];

    // Validasi tipe file
    $allowed_types = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png',
                     'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    $allowed_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'];

    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $is_valid_type = in_array($file['type'], $allowed_types) ||
                    in_array('.' . $file_extension, $allowed_extensions);

    if (!$is_valid_type) {
        header("Location: ../views/berkas/upload.php?error=file_type");
        exit;
    }

    // Validasi ukuran file (5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        header("Location: ../views/berkas/upload.php?error=file_size");
        exit;
    }

    // Tentukan folder berdasarkan jenis berkas
    $folder_map = [
        'Dokumen Identitas' => 'identitas',
        'Kartu Keluarga' => 'identitas',
        'Akta Kelahiran' => 'identitas',
        'Rapor Kelas X' => 'rapor',
        'Rapor Kelas XI' => 'rapor',
        'Rapor Kelas XII' => 'rapor',
        'Rapor KPP' => 'rapor',
        'Rapor KPA' => 'rapor',
        'Ijazah SD/MI' => 'ijazah',
        'Ijazah SMP/MTs' => 'ijazah',
        'Ijazah SMA/SMK/MA' => 'ijazah',
        'Foto Siswa' => 'foto'
    ];

    $folder = $folder_map[$jenis_berkas] ?? 'lainnya';

    // Upload file
    $uploaded_file = uploadFileBerkas($file, $folder);

    if (!$uploaded_file) {
        header("Location: ../views/berkas/upload.php?error=upload_failed");
        exit;
    }

    // Simpan ke database
    $file_path = $folder . '/' . $uploaded_file;
    $stmt = $conn->prepare("INSERT INTO berkas_siswa (siswa_nisn, jenis_berkas, nama_file, file_path, ukuran_file, keterangan, uploaded_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssisi", $siswa_nisn, $jenis_berkas, $file['name'], $file_path, $file['size'], $keterangan, $uploaded_by);

    if ($stmt->execute()) {
        header("Location: ../views/berkas/index.php?success=1");
    } else {
        // Hapus file yang sudah diupload jika gagal simpan ke database
        unlink("../uploads/$file_path");
        header("Location: ../views/berkas/upload.php?error=database");
    }
    exit;
}

// Hapus Berkas
if (isset($_GET['hapus'])) {
    $id = $_GET['hapus'];

    // Get file path sebelum dihapus
    $stmt = $conn->prepare("SELECT file_path FROM berkas_siswa WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $berkas = $result->fetch_assoc();

    if ($berkas) {
        // Hapus dari database
        $delete_stmt = $conn->prepare("DELETE FROM berkas_siswa WHERE id = ?");
        $delete_stmt->bind_param("i", $id);

        if ($delete_stmt->execute()) {
            // Hapus file fisik
            $file_path = "../uploads/" . $berkas['file_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            header("Location: ../views/berkas/index.php?deleted=1");
        } else {
            header("Location: ../views/berkas/index.php?error=delete_failed");
        }
    } else {
        header("Location: ../views/berkas/index.php?error=not_found");
    }
    exit;
}

// Get Berkas by Siswa (AJAX)
if (isset($_GET['get_berkas_siswa'])) {
    $siswa_nisn = $_GET['siswa_nisn'];

    $stmt = $conn->prepare("
        SELECT b.*, s.nama_lengkap, u.username as uploaded_by_name
        FROM berkas_siswa b
        JOIN siswa s ON b.siswa_nisn = s.nisn
        JOIN users u ON b.uploaded_by = u.id
        WHERE b.siswa_nisn = ?
        ORDER BY b.uploaded_at DESC
    ");
    $stmt->bind_param("s", $siswa_nisn);
    $stmt->execute();
    $result = $stmt->get_result();

    $berkas = [];
    while ($row = $result->fetch_assoc()) {
        $row['uploaded_at'] = date('d/m/Y H:i', strtotime($row['uploaded_at']));
        $row['ukuran_file_formatted'] = formatFileSize($row['ukuran_file']);
        $berkas[] = $row;
    }

    header('Content-Type: application/json');
    echo json_encode($berkas);
    exit;
}

// Function untuk upload file berkas
function uploadFileBerkas($file, $folder) {
    $target_dir = "../uploads/$folder/";

    // Buat folder jika belum ada
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0755, true);
    }

    // Generate nama file unik
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $file_name = uniqid() . "_" . time() . "." . $file_extension;
    $target_file = $target_dir . $file_name;

    if (move_uploaded_file($file['tmp_name'], $target_file)) {
        return $file_name;
    } else {
        return false;
    }
}

// Function untuk format ukuran file
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>