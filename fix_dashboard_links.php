<?php
/**
 * Script untuk memperbaiki link dashboard yang salah
 */

echo "=== MEMPERBAIKI LINK DASHBOARD ===\n";

// Fungsi untuk scan dan replace dalam file
function fixDashboardLinks($directory) {
    $files_fixed = 0;
    $total_replacements = 0;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $filepath = $file->getPathname();
            $content = file_get_contents($filepath);
            $original_content = $content;
            
            // Pattern untuk mencari link dashboard yang salah
            $patterns = [
                '/href=["\']\.\.\/dashboard\.php["\']/' => 'href="../dashboard/index.php"',
                '/href=["\']dashboard\.php["\']/' => 'href="dashboard/index.php"',
                '/Location:\s*\.\.\/dashboard\.php/' => 'Location: ../dashboard/index.php',
                '/Location:\s*dashboard\.php/' => 'Location: dashboard/index.php',
                '/Location:\s*views\/dashboard\.php/' => 'Location: views/dashboard/index.php'
            ];
            
            $file_replacements = 0;
            foreach ($patterns as $pattern => $replacement) {
                $new_content = preg_replace($pattern, $replacement, $content);
                if ($new_content !== $content) {
                    $matches = preg_match_all($pattern, $content);
                    $file_replacements += $matches;
                    $content = $new_content;
                }
            }
            
            if ($content !== $original_content) {
                file_put_contents($filepath, $content);
                echo "✅ Fixed: " . str_replace(getcwd() . DIRECTORY_SEPARATOR, '', $filepath) . " ($file_replacements replacements)\n";
                $files_fixed++;
                $total_replacements += $file_replacements;
            }
        }
    }
    
    return [$files_fixed, $total_replacements];
}

// Scan direktori views
echo "Scanning views directory...\n";
list($files_fixed, $total_replacements) = fixDashboardLinks('views');

echo "\n=== HASIL ===\n";
echo "✅ Files fixed: $files_fixed\n";
echo "✅ Total replacements: $total_replacements\n";

// Cek apakah ada file dashboard.php yang tidak seharusnya ada
echo "\n=== CEK FILE DASHBOARD.PHP ===\n";
$dashboard_files = [
    'views/dashboard.php',
    'dashboard.php'
];

foreach ($dashboard_files as $file) {
    if (file_exists($file)) {
        echo "⚠️ File $file ditemukan - mungkin perlu dihapus atau dipindah\n";
        
        // Tampilkan isi file untuk analisis
        $content = file_get_contents($file);
        $lines = explode("\n", $content);
        echo "   Preview (first 5 lines):\n";
        for ($i = 0; $i < min(5, count($lines)); $i++) {
            echo "   " . ($i + 1) . ": " . trim($lines[$i]) . "\n";
        }
        echo "   ...\n";
    } else {
        echo "✅ File $file tidak ada (bagus)\n";
    }
}

// Buat file redirect jika diperlukan
echo "\n=== MEMBUAT FILE REDIRECT ===\n";
$redirect_content = '<?php
// Redirect file untuk kompatibilitas
// File ini akan redirect ke dashboard/index.php

header("Location: dashboard/index.php");
exit;
?>';

// Buat redirect di views/dashboard.php jika tidak ada
if (!file_exists('views/dashboard.php')) {
    file_put_contents('views/dashboard.php', $redirect_content);
    echo "✅ Created redirect file: views/dashboard.php\n";
} else {
    echo "ℹ️ File views/dashboard.php sudah ada\n";
}

echo "\n=== VERIFIKASI LINK ===\n";
echo "Testing links yang sudah diperbaiki:\n";
echo "🔗 http://localhost:8000/views/dashboard/index.php (Target utama)\n";
echo "🔗 http://localhost:8000/views/dashboard.php (Redirect)\n";
echo "🔗 http://localhost:8000/views/siswa/index.php (Cek navbar)\n";
echo "🔗 http://localhost:8000/views/kelas/index.php (Cek navbar)\n";

echo "\n🎉 Perbaikan link dashboard selesai!\n";
?>
