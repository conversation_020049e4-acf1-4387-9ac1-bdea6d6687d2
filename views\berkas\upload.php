<?php
require_once '../../config/session_helper.php';
require_once '../../config/db.php';

// Pastikan user sudah login
requireLogin(true);

require_once '../../config/db.php';

// Get data siswa untuk dropdown
$siswa_result = $conn->query("SELECT s.nisn, s.nama_lengkap, k.nama_kelas FROM siswa s LEFT JOIN kelas k ON s.kelas_id = k.id WHERE s.status = 'Aktif' ORDER BY s.nama_lengkap");

// Pre-select siswa jika ada parameter
$selected_siswa = $_GET['siswa_nisn'] ?? '';

// Get jenis berkas options
$jenis_result = $conn->query("SHOW COLUMNS FROM berkas_siswa LIKE 'jenis_berkas'");
$jenis_row = $jenis_result->fetch_assoc();
$enum_string = $jenis_row['Type'];
preg_match_all("/'([^']+)'/", $enum_string, $matches);
$jenis_options = $matches[1];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Berkas Siswa - Aplikasi Siswa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-section {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0 8px 8px 0;
        }
        .required {
            color: #dc3545;
        }
        .file-drop-area {
            border: 2px dashed #28a745;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s;
            cursor: pointer;
        }
        .file-drop-area:hover {
            border-color: #20c997;
            background: #e9f7ef;
        }
        .file-drop-area.dragover {
            border-color: #20c997;
            background: #d4edda;
        }
        .file-preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            background: white;
        }
        .file-info {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard/index.php">
                <i class="fas fa-graduation-cap me-2"></i>Aplikasi Siswa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard/index.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Siswa</a>
                <a class="nav-link" href="../catatan/index.php">Catatan</a>
                <a class="nav-link" href="../absensi/index.php">Absensi</a>
                <a class="nav-link active" href="index.php">Berkas</a>
                <a class="nav-link" href="../../controllers/AuthController.php?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-cloud-upload-alt text-success me-2"></i>Upload Berkas Siswa</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/index.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Berkas Siswa</a></li>
                        <li class="breadcrumb-item active">Upload Berkas</li>
                    </ol>
                </nav>
            </div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Kembali
            </a>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php if ($_GET['error'] == 'file_type'): ?>
                    Tipe file tidak didukung! Hanya file PDF, JPG, PNG, DOC, DOCX yang diizinkan.
                <?php elseif ($_GET['error'] == 'file_size'): ?>
                    Ukuran file terlalu besar! Maksimal 5MB.
                <?php else: ?>
                    Terjadi kesalahan saat mengupload berkas!
                <?php endif; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Form Upload Berkas -->
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Form Upload Berkas</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="../../controllers/BerkasController.php" enctype="multipart/form-data" id="formUploadBerkas">
                    <!-- Informasi Siswa -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-user me-2"></i>Informasi Siswa</h6>
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Pilih Siswa <span class="required">*</span></label>
                                <select class="form-select" name="siswa_nisn" required id="selectSiswa">
                                    <option value="">-- Pilih Siswa --</option>
                                    <?php while ($siswa = $siswa_result->fetch_assoc()): ?>
                                        <option value="<?= $siswa['nisn'] ?>" <?= $selected_siswa == $siswa['nisn'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($siswa['nama_lengkap']) ?> 
                                            <?= $siswa['nama_kelas'] ? '(' . $siswa['nama_kelas'] . ')' : '' ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Jenis Berkas <span class="required">*</span></label>
                                <select class="form-select" name="jenis_berkas" required>
                                    <option value="">-- Pilih Jenis Berkas --</option>
                                    <?php foreach ($jenis_options as $jenis): ?>
                                        <option value="<?= $jenis ?>"><?= $jenis ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Upload File -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-file me-2"></i>File Berkas</h6>
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Pilih File <span class="required">*</span></label>
                                <div class="file-drop-area" id="fileDropArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                    <h5>Drag & Drop file di sini</h5>
                                    <p class="text-muted">atau klik untuk memilih file</p>
                                    <input type="file" name="file_berkas" id="fileInput" class="d-none" required 
                                           accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <button type="button" class="btn btn-outline-success" onclick="document.getElementById('fileInput').click()">
                                        <i class="fas fa-folder-open me-1"></i>Pilih File
                                    </button>
                                </div>
                                <div class="form-text">
                                    <strong>Format yang didukung:</strong> PDF, JPG, PNG, DOC, DOCX<br>
                                    <strong>Ukuran maksimal:</strong> 5MB
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Preview File</label>
                                <div id="filePreview" class="file-preview d-none">
                                    <div class="text-center">
                                        <i class="fas fa-file fa-3x text-muted mb-2"></i>
                                        <div id="fileName" class="fw-bold"></div>
                                        <div id="fileSize" class="file-info"></div>
                                        <div id="fileType" class="file-info"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Keterangan -->
                    <div class="form-section">
                        <h6 class="fw-bold mb-3"><i class="fas fa-comment me-2"></i>Keterangan (Opsional)</h6>
                        <div class="row">
                            <div class="col-12">
                                <label class="form-label">Keterangan Berkas</label>
                                <textarea class="form-control" name="keterangan" rows="3" 
                                          placeholder="Tambahkan keterangan untuk berkas ini..."></textarea>
                                <div class="form-text">
                                    Contoh: "Rapor semester 1", "Surat keterangan dokter", dll.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informasi Tambahan -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Informasi Upload</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Dokumen Identitas:</strong> KTP, Kartu Pelajar</li>
                                    <li><strong>Kartu Keluarga:</strong> Fotocopy KK</li>
                                    <li><strong>Akta Kelahiran:</strong> Fotocopy akta kelahiran</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>Rapor:</strong> Rapor per semester/kelas</li>
                                    <li><strong>Ijazah:</strong> Ijazah jenjang sebelumnya</li>
                                    <li><strong>Foto Siswa:</strong> Foto formal siswa</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Tombol Submit -->
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="fas fa-times me-1"></i>Batal
                        </button>
                        <button type="submit" name="upload_berkas" class="btn btn-success">
                            <i class="fas fa-upload me-1"></i>Upload Berkas
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const fileDropArea = document.getElementById('fileDropArea');
        const fileInput = document.getElementById('fileInput');
        const filePreview = document.getElementById('filePreview');

        // Drag and drop functionality
        fileDropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileDropArea.classList.add('dragover');
        });

        fileDropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileDropArea.classList.remove('dragover');
        });

        fileDropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileDropArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFilePreview(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                showFilePreview(e.target.files[0]);
            }
        });

        // Show file preview
        function showFilePreview(file) {
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const fileType = document.getElementById('fileType');

            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileType.textContent = file.type || 'Unknown';

            filePreview.classList.remove('d-none');

            // Update icon based on file type
            const icon = filePreview.querySelector('i');
            if (file.type.includes('pdf')) {
                icon.className = 'fas fa-file-pdf fa-3x text-danger mb-2';
            } else if (file.type.includes('image')) {
                icon.className = 'fas fa-file-image fa-3x text-primary mb-2';
            } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                icon.className = 'fas fa-file-word fa-3x text-info mb-2';
            } else {
                icon.className = 'fas fa-file fa-3x text-muted mb-2';
            }
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form validation
        document.getElementById('formUploadBerkas').addEventListener('submit', function(e) {
            const siswa = document.querySelector('[name="siswa_nisn"]').value;
            const jenis = document.querySelector('[name="jenis_berkas"]').value;
            const file = document.querySelector('[name="file_berkas"]').files[0];

            if (!siswa || !jenis || !file) {
                e.preventDefault();
                alert('Mohon lengkapi semua field yang wajib diisi!');
                return false;
            }

            // Check file size (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                e.preventDefault();
                alert('Ukuran file terlalu besar! Maksimal 5MB.');
                return false;
            }

            // Check file type
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 
                                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'];
            
            const isValidType = allowedTypes.includes(file.type) || 
                              allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
            
            if (!isValidType) {
                e.preventDefault();
                alert('Tipe file tidak didukung! Hanya file PDF, JPG, PNG, DOC, DOCX yang diizinkan.');
                return false;
            }
        });
    </script>
</body>
</html>
