<?php
session_start();
require_once '../../config/db.php';

$id = $_GET['id'] ?? '';
$query = "SELECT * FROM kelas WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $id);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

if (!$kelas) {
    header('Location: index.php');
    exit;
}

// Get siswa dalam kelas
$siswa_query = "SELECT * FROM siswa WHERE kelas_id = ? ORDER BY nama_lengkap";
$siswa_stmt = $conn->prepare($siswa_query);
$siswa_stmt->bind_param("i", $id);
$siswa_stmt->execute();
$siswa_result = $siswa_stmt->get_result();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Kelas: <?php echo htmlspecialchars($kelas['nama_kelas']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .detail-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }
        .info-value {
            color: #333;
        }
        .badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="../siswa/index.php">Data Siswa</a>
                <a class="nav-link active" href="index.php">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../../logout.php">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-school me-2"></i>Detail Kelas: <?php echo htmlspecialchars($kelas['nama_kelas']); ?></h4>
            </div>
            <div>
                <a href="edit.php?id=<?php echo $kelas['id']; ?>" class="btn btn-warning btn-sm me-2">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <a href="index.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>

        <!-- Detail Info -->
        <div class="detail-container mb-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Nama Kelas:</span>
                        <span class="info-value"><strong><?php echo htmlspecialchars($kelas['nama_kelas']); ?></strong></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Tingkat:</span>
                        <span class="info-value">
                            <span class="badge bg-primary"><?php echo htmlspecialchars($kelas['tingkat'] ?? 'KPA Pemrograman Web'); ?></span>
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Kurikulum:</span>
                        <span class="info-value">
                            <span class="badge bg-info"><?php echo htmlspecialchars($kelas['kurikulum'] ?? 'Kurikulum Merdeka'); ?></span>
                        </span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">Tahun Pelajaran:</span>
                        <span class="info-value"><?php echo htmlspecialchars($kelas['tahun_pelajaran'] ?? '2024/2025'); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Wali Kelas:</span>
                        <span class="info-value"><?php echo htmlspecialchars($kelas['wali_kelas'] ?? 'Y. Guntur Cahyo Dewantoro, ST.'); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">Kapasitas:</span>
                        <span class="info-value">
                            <span class="badge bg-success"><?php echo $siswa_result->num_rows; ?>/<?php echo $kelas['kapasitas'] ?? '30'; ?></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daftar Siswa -->
        <div class="detail-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-users me-2"></i>Daftar Siswa</h5>
                <span class="badge bg-primary fs-6"><?php echo $siswa_result->num_rows; ?> siswa</span>
            </div>
            
            <?php if ($siswa_result->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th>No</th>
                            <th>NIS</th>
                            <th>Nama Lengkap</th>
                            <th>Jenis Kelamin</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        while ($siswa = $siswa_result->fetch_assoc()): 
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><strong>A25.285</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <?php echo htmlspecialchars($siswa['nama_lengkap']); ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo $siswa['jenis_kelamin']; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-success">Aktif</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="../siswa/detail.php?nisn=<?php echo $siswa['nisn']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="../siswa/edit.php?nisn=<?php echo $siswa['nisn']; ?>" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Belum ada siswa</h5>
                <p class="text-muted">Kelas ini belum memiliki siswa.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
