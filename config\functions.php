<?php
// Fungsi Upload File
function uploadFile($file, $folder) {
    $target_dir = "../uploads/$folder/";
    $file_name = uniqid() . "_" . basename($file["name"]);
    $target_file = $target_dir . $file_name;
    $upload_ok = 1;

    // Cek tipe file
    $file_type = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    if (!in_array($file_type, ['jpg', 'png', 'pdf', 'doc', 'docx'])) {
        $upload_ok = 0;
    }

    // Cek ukuran (max 5MB)
    if ($file["size"] > 5000000) {
        $upload_ok = 0;
    }

    if ($upload_ok && move_uploaded_file($file["tmp_name"], $target_file)) {
        return $file_name;
    } else {
        return false;
    }
}

// Fungsi Hash Password
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT);
}

// Fungsi Verifikasi Password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Fungsi Format File Size
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';

    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));

    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

// Fungsi Get File Icon
function getFileIcon($extension) {
    $icons = [
        'pdf' => 'fas fa-file-pdf text-danger',
        'doc' => 'fas fa-file-word text-primary',
        'docx' => 'fas fa-file-word text-primary',
        'jpg' => 'fas fa-file-image text-warning',
        'jpeg' => 'fas fa-file-image text-warning',
        'png' => 'fas fa-file-image text-warning',
        'default' => 'fas fa-file text-secondary'
    ];

    return $icons[strtolower($extension)] ?? $icons['default'];
}
?>