<?php
// Test file untuk fungsi siswa
session_start();
$_SESSION['user_id'] = 1; // Set session untuk testing

require_once 'config/db.php';

echo "<h2>Test Fungsi Siswa</h2>";

// Test 1: Cek koneksi database
echo "<h3>1. Test Koneksi Database</h3>";
if ($conn) {
    echo "✅ Koneksi database berhasil<br>";
} else {
    echo "❌ Koneksi database gagal<br>";
}

// Test 2: Cek tabel siswa
echo "<h3>2. Test Tabel Siswa</h3>";
$result = $conn->query("SHOW TABLES LIKE 'siswa'");
if ($result->num_rows > 0) {
    echo "✅ Tabel siswa ada<br>";
    
    // Cek struktur tabel
    $columns = $conn->query("DESCRIBE siswa");
    echo "<strong>Kolom tabel siswa:</strong><br>";
    while ($col = $columns->fetch_assoc()) {
        echo "- " . $col['Field'] . " (" . $col['Type'] . ")<br>";
    }
} else {
    echo "❌ Tabel siswa tidak ada<br>";
}

// Test 3: Cek data siswa
echo "<h3>3. Test Data Siswa</h3>";
$siswa_result = $conn->query("SELECT COUNT(*) as total FROM siswa");
$total = $siswa_result->fetch_assoc()['total'];
echo "Total siswa: <strong>$total</strong><br>";

if ($total > 0) {
    echo "<strong>Data siswa:</strong><br>";
    $data_siswa = $conn->query("SELECT nisn, nama_lengkap, jenis_kelamin FROM siswa LIMIT 5");
    while ($siswa = $data_siswa->fetch_assoc()) {
        echo "- " . $siswa['nama_lengkap'] . " (NISN: " . $siswa['nisn'] . ", " . $siswa['jenis_kelamin'] . ")<br>";
    }
}

// Test 4: Test link edit dan delete
echo "<h3>4. Test Link Edit dan Delete</h3>";
if ($total > 0) {
    $first_siswa = $conn->query("SELECT nisn, nama_lengkap FROM siswa LIMIT 1")->fetch_assoc();
    $nisn = $first_siswa['nisn'];
    $nama = $first_siswa['nama_lengkap'];
    
    echo "<strong>Contoh siswa:</strong> $nama (NISN: $nisn)<br>";
    echo "<strong>Link Edit:</strong> <a href='views/siswa/edit.php?nisn=$nisn' target='_blank'>Edit $nama</a><br>";
    echo "<strong>Link Delete:</strong> <a href='controllers/SiswaController.php?hapus=$nisn' onclick='return confirm(\"Yakin hapus?\")'>Hapus $nama</a><br>";
} else {
    echo "Tidak ada data siswa untuk ditest<br>";
}

// Test 5: Test controller files
echo "<h3>5. Test Controller Files</h3>";
$controller_file = 'controllers/SiswaController.php';
if (file_exists($controller_file)) {
    echo "✅ File $controller_file ada<br>";
} else {
    echo "❌ File $controller_file tidak ada<br>";
}

$edit_file = 'views/siswa/edit.php';
if (file_exists($edit_file)) {
    echo "✅ File $edit_file ada<br>";
} else {
    echo "❌ File $edit_file tidak ada<br>";
}

echo "<hr>";
echo "<p><a href='views/siswa/index.php'>Kembali ke Data Siswa</a></p>";
?>
