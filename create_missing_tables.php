<?php
require_once 'config/db.php';

echo "=== MEMBUAT TABEL YANG HILANG ===\n";

// 1. Cek dan buat tabel catatan_siswa
$check_catatan = $conn->query("SHOW TABLES LIKE 'catatan_siswa'");
if ($check_catatan->num_rows == 0) {
    echo "Membuat tabel catatan_siswa...\n";
    $sql_catatan = "
    CREATE TABLE catatan_siswa (
        id INT AUTO_INCREMENT PRIMARY KEY,
        siswa_nisn VARCHAR(20) NOT NULL,
        jenis_catatan ENUM(
            'Catatan Pamong MM (XI)',
            'Catatan Pamong MP (KPP)',
            'Catatan Pamong MT (X)',
            'Catatan Pamong MU (XII & KPA)',
            'Catatan Wali Kelas KPA',
            'Catatan Wali Kelas KPP',
            'Catatan Wali Kelas X',
            'Catatan Wali Kelas XI',
            'Catatan Wali Kelas XII',
            'Konseling BK',
            '<PERSON>stasi (BK)',
            '<PERSON>elanggaran',
            '<PERSON><PERSON>nggaran (BK)',
            'BK Lainnya',
            'Catatan BK Lainnya'
        ) NOT NULL,
        judul_catatan VARCHAR(255) NOT NULL,
        isi_catatan TEXT NOT NULL,
        tingkat_prioritas ENUM('Rendah','Sedang','Tinggi','Sangat Tinggi') DEFAULT 'Sedang',
        status ENUM('Aktif','Selesai','Ditunda','Dibatalkan') DEFAULT 'Aktif',
        tindak_lanjut TEXT,
        tanggal_tindak_lanjut DATE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id)
    )";
    
    if ($conn->query($sql_catatan)) {
        echo "✓ Tabel catatan_siswa berhasil dibuat\n";
    } else {
        echo "✗ Error: " . $conn->error . "\n";
    }
} else {
    echo "Tabel catatan_siswa sudah ada\n";
}

// 2. Cek dan buat tabel statistik_absensi
$check_statistik = $conn->query("SHOW TABLES LIKE 'statistik_absensi'");
if ($check_statistik->num_rows == 0) {
    echo "Membuat tabel statistik_absensi...\n";
    $sql_statistik = "
    CREATE TABLE statistik_absensi (
        id INT AUTO_INCREMENT PRIMARY KEY,
        siswa_nisn VARCHAR(20) NOT NULL,
        bulan INT NOT NULL,
        tahun INT NOT NULL,
        total_hadir INT DEFAULT 0,
        total_sakit INT DEFAULT 0,
        total_izin INT DEFAULT 0,
        total_alpa INT DEFAULT 0,
        persentase_kehadiran DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
        UNIQUE KEY unique_siswa_bulan (siswa_nisn, bulan, tahun)
    )";
    
    if ($conn->query($sql_statistik)) {
        echo "✓ Tabel statistik_absensi berhasil dibuat\n";
    } else {
        echo "✗ Error: " . $conn->error . "\n";
    }
} else {
    echo "Tabel statistik_absensi sudah ada\n";
}

// 3. Cek dan buat tabel kategori_catatan
$check_kategori = $conn->query("SHOW TABLES LIKE 'kategori_catatan'");
if ($check_kategori->num_rows == 0) {
    echo "Membuat tabel kategori_catatan...\n";
    $sql_kategori = "
    CREATE TABLE kategori_catatan (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nama_kategori VARCHAR(100) NOT NULL,
        deskripsi TEXT,
        warna VARCHAR(7) DEFAULT '#007bff',
        icon VARCHAR(50) DEFAULT 'fas fa-sticky-note',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql_kategori)) {
        echo "✓ Tabel kategori_catatan berhasil dibuat\n";
        
        // Insert data default
        $insert_kategori = "
        INSERT INTO kategori_catatan (nama_kategori, deskripsi, warna, icon) VALUES
        ('Pamong', 'Catatan dari Pamong/Pembimbing', '#28a745', 'fas fa-user-tie'),
        ('Wali Kelas', 'Catatan dari Wali Kelas', '#17a2b8', 'fas fa-chalkboard-teacher'),
        ('Konseling BK', 'Catatan Bimbingan Konseling', '#ffc107', 'fas fa-comments'),
        ('Prestasi', 'Catatan Prestasi Siswa', '#28a745', 'fas fa-trophy'),
        ('Pelanggaran', 'Catatan Pelanggaran', '#dc3545', 'fas fa-exclamation-triangle'),
        ('BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'fas fa-clipboard')
        ";
        
        if ($conn->query($insert_kategori)) {
            echo "✓ Data kategori default berhasil diinsert\n";
        } else {
            echo "✗ Error insert kategori: " . $conn->error . "\n";
        }
    } else {
        echo "✗ Error: " . $conn->error . "\n";
    }
} else {
    echo "Tabel kategori_catatan sudah ada\n";
}

// 4. Update tabel siswa dengan field tambahan
echo "\nMenambahkan field baru ke tabel siswa...\n";
$alter_queries = [
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nik VARCHAR(20) AFTER nisn",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS no_kk VARCHAR(20) AFTER nik",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS golongan_darah ENUM('A','B','AB','O') AFTER jenis_kelamin",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS agama VARCHAR(20) AFTER golongan_darah",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nama_ayah VARCHAR(100) AFTER alamat",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS nama_ibu VARCHAR(100) AFTER nama_ayah",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pekerjaan_ayah VARCHAR(50) AFTER nama_ibu",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS pekerjaan_ibu VARCHAR(50) AFTER pekerjaan_ayah",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS no_telepon_ortu VARCHAR(15) AFTER no_telepon",
    "ALTER TABLE siswa ADD COLUMN IF NOT EXISTS status ENUM('Aktif','Tidak Aktif','Lulus','Pindah') DEFAULT 'Aktif' AFTER foto"
];

foreach ($alter_queries as $query) {
    // MySQL tidak mendukung IF NOT EXISTS untuk ALTER TABLE ADD COLUMN
    // Jadi kita cek manual
    $field_name = '';
    if (preg_match('/ADD COLUMN IF NOT EXISTS (\w+)/', $query, $matches)) {
        $field_name = $matches[1];
        $check_field = $conn->query("SHOW COLUMNS FROM siswa LIKE '$field_name'");
        if ($check_field->num_rows > 0) {
            echo "Field $field_name sudah ada\n";
            continue;
        }
        // Hapus IF NOT EXISTS dari query
        $query = str_replace(' IF NOT EXISTS', '', $query);
    }
    
    if ($conn->query($query)) {
        echo "✓ Field $field_name berhasil ditambahkan\n";
    } else {
        echo "✗ Error menambah field $field_name: " . $conn->error . "\n";
    }
}

echo "\n=== VERIFIKASI TABEL FINAL ===\n";
$result = $conn->query("SHOW TABLES");
echo "Tabel yang tersedia:\n";
while ($row = $result->fetch_array()) {
    echo "- " . $row[0] . "\n";
}

$conn->close();
echo "\n🎉 Setup database selesai!\n";
?>
