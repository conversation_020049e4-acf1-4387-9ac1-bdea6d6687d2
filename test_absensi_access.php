<?php
// Test akses halaman absensi
session_start();
$_SESSION['user_id'] = 1; // Set session untuk testing
$_SESSION['role'] = 'admin';

require_once 'config/db.php';

echo "<h2>Test Akses <PERSON>i</h2>";

// Test 1: Cek session
echo "<h3>1. Session Status</h3>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
echo "Role: " . ($_SESSION['role'] ?? 'Not set') . "<br>";

// Test 2: Cek koneksi database
echo "<h3>2. Database Connection</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// Test 3: Test query absensi
echo "<h3>3. Test Query Absensi</h3>";
try {
    $query = "
        SELECT a.*, s.nama_lengkap, k.nama_kelas
        FROM absensi a
        JOIN siswa s ON a.siswa_nisn = s.nisn
        LEFT JOIN kelas k ON s.kelas_id = k.id
        ORDER BY a.tanggal DESC, s.nama_lengkap
        LIMIT 5
    ";
    
    $result = $conn->query($query);
    if ($result) {
        echo "✅ Query berhasil, ditemukan " . $result->num_rows . " data<br>";
        
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
            echo "<tr><th>Tanggal</th><th>Siswa</th><th>Kelas</th><th>Status</th><th>Catatan</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . date('d/m/Y', strtotime($row['tanggal'])) . "</td>";
                echo "<td>" . htmlspecialchars($row['nama_lengkap']) . "</td>";
                echo "<td>" . htmlspecialchars($row['nama_kelas'] ?? 'Tidak ada kelas') . "</td>";
                echo "<td>" . $row['keterangan'] . "</td>";
                echo "<td>" . htmlspecialchars($row['catatan'] ?? '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "❌ Query gagal: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 4: Test statistik absensi
echo "<h3>4. Test Statistik Absensi Hari Ini</h3>";
try {
    $stats_query = "
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN keterangan = 'Hadir' THEN 1 ELSE 0 END) as hadir,
            SUM(CASE WHEN keterangan = 'Sakit' THEN 1 ELSE 0 END) as sakit,
            SUM(CASE WHEN keterangan = 'Izin' THEN 1 ELSE 0 END) as izin,
            SUM(CASE WHEN keterangan = 'Alpa' THEN 1 ELSE 0 END) as alpa
        FROM absensi
        WHERE DATE(tanggal) = CURDATE()
    ";
    
    $stats_result = $conn->query($stats_query);
    if ($stats_result) {
        $stats = $stats_result->fetch_assoc();
        echo "✅ Statistik hari ini:<br>";
        echo "- Total: " . $stats['total'] . "<br>";
        echo "- Hadir: " . $stats['hadir'] . "<br>";
        echo "- Sakit: " . $stats['sakit'] . "<br>";
        echo "- Izin: " . $stats['izin'] . "<br>";
        echo "- Alpa: " . $stats['alpa'] . "<br>";
    } else {
        echo "❌ Query statistik gagal: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Error statistik: " . $e->getMessage() . "<br>";
}

// Test 5: Cek file-file absensi
echo "<h3>5. File Absensi</h3>";
$files_to_check = [
    'views/absensi/index.php',
    'views/absensi/tambah.php',
    'controllers/AbsensiController.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file ada<br>";
    } else {
        echo "❌ $file tidak ada<br>";
    }
}

echo "<hr>";
echo "<h3>Links untuk Testing:</h3>";
echo "<p><a href='views/absensi/index.php' target='_blank'>🔗 Halaman Absensi Asli</a></p>";
echo "<p><a href='demo_absensi.php' target='_blank'>🎭 Demo Absensi</a></p>";
echo "<p><a href='views/absensi/tambah.php' target='_blank'>➕ Tambah Absensi</a></p>";

$conn->close();
?>
