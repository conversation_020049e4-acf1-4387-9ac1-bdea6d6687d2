<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Siswa - SISWA APP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn-group .btn {
            margin-right: 2px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>SISWA APP
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">Dashboard</a>
                <a class="nav-link active" href="#">Data Siswa</a>
                <a class="nav-link" href="#">Data Kelas</a>
                <a class="nav-link" href="#">Management</a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> INSTRUCTOR
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4><i class="fas fa-users me-2"></i>Data Siswa</h4>
                <small class="text-muted">Data siswa sekolah - 2024/2025 (Tahun Berjalan)</small>
            </div>
            <div>
                <a href="#" class="btn btn-primary btn-sm me-2">
                    <i class="fas fa-plus"></i> Tambah Siswa
                </a>
                <button class="btn btn-outline-secondary btn-sm me-2">
                    <i class="fas fa-cog"></i> Default
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-th"></i> Cards
                </button>
            </div>
        </div>

        <!-- Alert Messages -->
        <div class="alert alert-success alert-dismissible fade show" id="successAlert" style="display: none;">
            <i class="fas fa-check-circle me-2"></i><span id="successMessage"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">3</div>
                    <div class="text-muted">TOTAL SISWA</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">0</div>
                    <div class="text-muted">LAKI-LAKI</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">0</div>
                    <div class="text-muted">PEREMPUAN</div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6><i class="fas fa-list me-2"></i>Daftar Siswa (3 items)</h6>
                <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" style="width: auto;">
                        <option>Semua Kelas</option>
                    </select>
                    <select class="form-select form-select-sm" style="width: auto;">
                        <option>Semua Jenis Kelamin</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th>No</th>
                            <th>NIS</th>
                            <th>Nama Lengkap</th>
                            <th>Jenis Kelamin</th>
                            <th>Kelas</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><strong>A25.285</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    Ahmad Rizki Pratama
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">L</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Kelas X-1</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Aktif</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Lihat Detail" onclick="showDetail('Ahmad Rizki Pratama')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit Data" onclick="showEdit('Ahmad Rizki Pratama')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus Data" onclick="confirmDelete('A25.285', 'Ahmad Rizki Pratama')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td><strong>A25.285</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    Siti Nurhaliza
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">P</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Kelas X-1</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Aktif</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Lihat Detail" onclick="showDetail('Siti Nurhaliza')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit Data" onclick="showEdit('Siti Nurhaliza')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus Data" onclick="confirmDelete('A25.286', 'Siti Nurhaliza')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td><strong>A25.285</strong></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    Budi Santoso
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">L</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Kelas X-2</span>
                            </td>
                            <td>
                                <span class="badge bg-success">Aktif</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" title="Lihat Detail" onclick="showDetail('Budi Santoso')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="Edit Data" onclick="showEdit('Budi Santoso')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" title="Hapus Data" onclick="confirmDelete('A25.287', 'Budi Santoso')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <small class="text-muted">Total 1 data ditampilkan</small>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <span class="page-link">Previous</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link">Next</span>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showDetail(nama) {
            Swal.fire({
                title: 'Detail Siswa',
                html: `
                    <div class="text-start">
                        <h5>${nama}</h5>
                        <hr>
                        <p><strong>NISN:</strong> A25.285</p>
                        <p><strong>Jenis Kelamin:</strong> Laki-laki</p>
                        <p><strong>Kelas:</strong> X-1</p>
                        <p><strong>Status:</strong> Aktif</p>
                        <p><strong>Alamat:</strong> Jl. Contoh No. 123</p>
                        <p><strong>No. Telepon:</strong> 081234567890</p>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'Tutup',
                width: '500px'
            });
        }

        function showEdit(nama) {
            Swal.fire({
                title: 'Edit Data Siswa',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Nama Lengkap</label>
                            <input type="text" class="form-control" value="${nama}" id="editNama">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">NISN</label>
                            <input type="text" class="form-control" value="A25.285" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Jenis Kelamin</label>
                            <select class="form-select" id="editJenisKelamin">
                                <option value="L">Laki-laki</option>
                                <option value="P">Perempuan</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Kelas</label>
                            <select class="form-select" id="editKelas">
                                <option value="X-1">Kelas X-1</option>
                                <option value="X-2">Kelas X-2</option>
                                <option value="XI-1">Kelas XI-1</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save me-1"></i>Simpan',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                width: '500px',
                preConfirm: () => {
                    const nama = document.getElementById('editNama').value;
                    const jenisKelamin = document.getElementById('editJenisKelamin').value;
                    const kelas = document.getElementById('editKelas').value;

                    if (!nama) {
                        Swal.showValidationMessage('Nama tidak boleh kosong');
                        return false;
                    }

                    return { nama, jenisKelamin, kelas };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Simulate update success
                    showSuccessMessage('Data siswa berhasil diperbarui!');
                }
            });
        }

        function confirmDelete(nisn, nama) {
            Swal.fire({
                title: 'Konfirmasi Hapus',
                html: `Apakah Anda yakin ingin menghapus data siswa:<br><strong>${nama}</strong><br>NISN: ${nisn}?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash me-1"></i>Ya, Hapus!',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Menghapus...',
                        text: 'Mohon tunggu sebentar',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Simulate delete process
                    setTimeout(() => {
                        Swal.close();
                        showSuccessMessage('Data siswa berhasil dihapus!');

                        // Remove row from table (demo purpose)
                        const rows = document.querySelectorAll('tbody tr');
                        rows.forEach(row => {
                            if (row.textContent.includes(nama)) {
                                row.remove();
                            }
                        });
                    }, 2000);
                }
            });
        }

        function showSuccessMessage(message) {
            const alert = document.getElementById('successAlert');
            const messageSpan = document.getElementById('successMessage');
            messageSpan.textContent = message;
            alert.style.display = 'block';
            alert.classList.add('show');

            // Auto hide after 5 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 150);
            }, 5000);
        }
    </script>
</body>
</html>
