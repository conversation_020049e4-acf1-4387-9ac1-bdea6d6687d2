-- ===================================
-- DATABASE UPGRADE UNTUK APLIKASI SISWA MODERN
-- ===================================

-- 1. UPGRADE TABEL SISWA (Tambah field yang diperlukan)
ALTER TABLE siswa 
ADD COLUMN nik VARCHAR(20) AFTER nisn,
ADD COLUMN no_kk VARCHAR(20) AFTER nik,
ADD COLUMN golongan_darah ENUM('A','B','AB','O') AFTER jenis_kelamin,
ADD COLUMN agama VARCHAR(20) AFTER golongan_darah,
ADD COLUMN nama_ayah VARCHAR(100) AFTER alamat,
ADD COLUMN nama_ibu VARCHAR(100) AFTER nama_ayah,
ADD COLUMN pekerjaan_ayah VARCHAR(50) AFTER nama_ibu,
ADD COLUMN pekerjaan_ibu VARCHAR(50) AFTER pekerjaan_ayah,
ADD COLUMN no_telepon_ortu VARCHAR(15) AFTER no_telepon,
ADD COLUMN status ENUM('Aktif','Tidak Aktif','Lulus','Pindah') DEFAULT 'Aktif' AFTER foto;

-- 2. UPGRADE TABEL ABSENSI (Tambah field jam masuk/keluar)
ALTER TABLE absensi 
ADD COLUMN jam_masuk TIME AFTER tanggal,
ADD COLUMN jam_keluar TIME AFTER jam_masuk,
ADD COLUMN status_kehadiran ENUM('Tepat Waktu','Terlambat','Pulang Cepat') AFTER keterangan;

-- 3. BUAT TABEL CATATAN SISWA
CREATE TABLE IF NOT EXISTS catatan_siswa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    siswa_nisn VARCHAR(20) NOT NULL,
    jenis_catatan ENUM(
        'Catatan Pamong MM (XI)',
        'Catatan Pamong MP (KPP)',
        'Catatan Pamong MT (X)',
        'Catatan Pamong MU (XII & KPA)',
        'Catatan Wali Kelas KPA',
        'Catatan Wali Kelas KPP',
        'Catatan Wali Kelas X',
        'Catatan Wali Kelas XI',
        'Catatan Wali Kelas XII',
        'Konseling BK',
        'Prestasi (BK)',
        'Pelanggaran',
        'Pelanggaran (BK)',
        'BK Lainnya',
        'Catatan BK Lainnya'
    ) NOT NULL,
    judul_catatan VARCHAR(255) NOT NULL,
    isi_catatan TEXT NOT NULL,
    tingkat_prioritas ENUM('Rendah','Sedang','Tinggi','Sangat Tinggi') DEFAULT 'Sedang',
    status ENUM('Aktif','Selesai','Ditunda','Dibatalkan') DEFAULT 'Aktif',
    tindak_lanjut TEXT,
    tanggal_tindak_lanjut DATE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 4. BUAT TABEL BERKAS SISWA (Mengganti yang lama)
DROP TABLE IF EXISTS berkas_siswa;
CREATE TABLE berkas_siswa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    siswa_nisn VARCHAR(20) NOT NULL,
    jenis_berkas ENUM(
        'Dokumen Identitas',
        'Kartu Keluarga',
        'Akta Kelahiran',
        'Rapor Kelas X',
        'Rapor Kelas XI', 
        'Rapor Kelas XII',
        'Rapor KPP',
        'Rapor KPA',
        'Ijazah SD/MI',
        'Ijazah SMP/MTs',
        'Ijazah SMA/SMK/MA',
        'Foto Siswa'
    ) NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    ukuran_file INT,
    keterangan TEXT,
    uploaded_by INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- 5. BUAT TABEL STATISTIK ABSENSI (untuk dashboard)
CREATE TABLE IF NOT EXISTS statistik_absensi (
    id INT AUTO_INCREMENT PRIMARY KEY,
    siswa_nisn VARCHAR(20) NOT NULL,
    bulan INT NOT NULL,
    tahun INT NOT NULL,
    total_hadir INT DEFAULT 0,
    total_sakit INT DEFAULT 0,
    total_izin INT DEFAULT 0,
    total_alpa INT DEFAULT 0,
    persentase_kehadiran DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
    UNIQUE KEY unique_siswa_bulan (siswa_nisn, bulan, tahun)
);

-- 6. BUAT TABEL KATEGORI CATATAN (untuk fleksibilitas)
CREATE TABLE IF NOT EXISTS kategori_catatan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_kategori VARCHAR(100) NOT NULL,
    deskripsi TEXT,
    warna VARCHAR(7) DEFAULT '#007bff',
    icon VARCHAR(50) DEFAULT 'fas fa-sticky-note',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. INSERT DATA KATEGORI CATATAN DEFAULT
INSERT INTO kategori_catatan (nama_kategori, deskripsi, warna, icon) VALUES
('Pamong', 'Catatan dari Pamong/Pembimbing', '#28a745', 'fas fa-user-tie'),
('Wali Kelas', 'Catatan dari Wali Kelas', '#17a2b8', 'fas fa-chalkboard-teacher'),
('Konseling BK', 'Catatan Bimbingan Konseling', '#ffc107', 'fas fa-comments'),
('Prestasi', 'Catatan Prestasi Siswa', '#28a745', 'fas fa-trophy'),
('Pelanggaran', 'Catatan Pelanggaran', '#dc3545', 'fas fa-exclamation-triangle'),
('BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'fas fa-clipboard');

-- 8. BUAT INDEKS UNTUK PERFORMA (dengan IF NOT EXISTS)
CREATE INDEX IF NOT EXISTS idx_siswa_kelas ON siswa(kelas_id);
CREATE INDEX IF NOT EXISTS idx_absensi_tanggal ON absensi(tanggal);
CREATE INDEX IF NOT EXISTS idx_absensi_siswa ON absensi(siswa_nisn);
CREATE INDEX IF NOT EXISTS idx_catatan_siswa ON catatan_siswa(siswa_nisn);
CREATE INDEX IF NOT EXISTS idx_catatan_tanggal ON catatan_siswa(created_at);
CREATE INDEX IF NOT EXISTS idx_berkas_siswa ON berkas_siswa(siswa_nisn);
CREATE INDEX IF NOT EXISTS idx_berkas_jenis ON berkas_siswa(jenis_berkas);

-- 9. BUAT VIEW UNTUK DASHBOARD
CREATE OR REPLACE VIEW view_dashboard_siswa AS
SELECT 
    s.nisn,
    s.nama_lengkap,
    s.kelas_id,
    k.nama_kelas,
    s.status,
    COUNT(DISTINCT a.id) as total_absensi,
    COUNT(DISTINCT c.id) as total_catatan,
    COUNT(DISTINCT b.id) as total_berkas,
    COALESCE(st.persentase_kehadiran, 0) as persentase_kehadiran
FROM siswa s
LEFT JOIN kelas k ON s.kelas_id = k.id
LEFT JOIN absensi a ON s.nisn = a.siswa_nisn
LEFT JOIN catatan_siswa c ON s.nisn = c.siswa_nisn
LEFT JOIN berkas_siswa b ON s.nisn = b.siswa_nisn
LEFT JOIN statistik_absensi st ON s.nisn = st.siswa_nisn 
    AND st.bulan = MONTH(CURRENT_DATE()) 
    AND st.tahun = YEAR(CURRENT_DATE())
GROUP BY s.nisn;
