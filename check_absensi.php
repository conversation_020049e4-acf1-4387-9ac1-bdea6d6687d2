<?php
require_once 'config/db.php';

echo "=== CEK TABEL ABSENSI ===\n";

// Cek apakah tabel absensi ada
$result = $conn->query("SHOW TABLES LIKE 'absensi'");
if ($result->num_rows > 0) {
    echo "✅ Tabel absensi ada\n\n";
    
    // Tampilkan struktur tabel
    echo "=== STRUKTUR TABEL ABSENSI ===\n";
    $desc = $conn->query("DESCRIBE absensi");
    while ($row = $desc->fetch_assoc()) {
        echo $row['Field'] . " | " . $row['Type'] . " | " . $row['Null'] . " | " . $row['Key'] . "\n";
    }
    
    // Cek data absensi
    echo "\n=== DATA ABSENSI ===\n";
    $data = $conn->query("SELECT COUNT(*) as total FROM absensi");
    $total = $data->fetch_assoc()['total'];
    echo "Total data absensi: $total\n";
    
    if ($total > 0) {
        echo "\nContoh data absensi:\n";
        $sample = $conn->query("SELECT * FROM absensi LIMIT 3");
        while ($row = $sample->fetch_assoc()) {
            echo "- " . $row['siswa_nisn'] . " | " . $row['tanggal'] . " | " . $row['keterangan'] . "\n";
        }
    }
    
} else {
    echo "❌ Tabel absensi tidak ada\n";
    echo "Membuat tabel absensi...\n";
    
    $create_sql = "
    CREATE TABLE absensi (
        id INT AUTO_INCREMENT PRIMARY KEY,
        siswa_nisn VARCHAR(20) NOT NULL,
        tanggal DATE NOT NULL,
        jam_masuk TIME NULL,
        jam_keluar TIME NULL,
        keterangan ENUM('Hadir', 'Sakit', 'Izin', 'Alpa') NOT NULL,
        catatan TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
        UNIQUE KEY unique_siswa_tanggal (siswa_nisn, tanggal)
    )";
    
    if ($conn->query($create_sql)) {
        echo "✅ Tabel absensi berhasil dibuat\n";
    } else {
        echo "❌ Error membuat tabel absensi: " . $conn->error . "\n";
    }
}

// Cek tabel statistik_absensi
echo "\n=== CEK TABEL STATISTIK_ABSENSI ===\n";
$result2 = $conn->query("SHOW TABLES LIKE 'statistik_absensi'");
if ($result2->num_rows > 0) {
    echo "✅ Tabel statistik_absensi ada\n";
} else {
    echo "❌ Tabel statistik_absensi tidak ada\n";
    echo "Membuat tabel statistik_absensi...\n";
    
    $create_stats_sql = "
    CREATE TABLE statistik_absensi (
        id INT AUTO_INCREMENT PRIMARY KEY,
        siswa_nisn VARCHAR(20) NOT NULL,
        bulan INT NOT NULL,
        tahun INT NOT NULL,
        total_hadir INT DEFAULT 0,
        total_sakit INT DEFAULT 0,
        total_izin INT DEFAULT 0,
        total_alpa INT DEFAULT 0,
        persentase_kehadiran DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (siswa_nisn) REFERENCES siswa(nisn) ON DELETE CASCADE,
        UNIQUE KEY unique_siswa_bulan (siswa_nisn, bulan, tahun)
    )";
    
    if ($conn->query($create_stats_sql)) {
        echo "✅ Tabel statistik_absensi berhasil dibuat\n";
    } else {
        echo "❌ Error membuat tabel statistik_absensi: " . $conn->error . "\n";
    }
}

echo "\n🎉 Pengecekan selesai!\n";
$conn->close();
?>
